/*
数据发布和订阅类的构造函数和析构函数
*/

#include "robot_controller/Def_Class.h"

namespace DATA_PROC{
    Eigen::VectorXd jnt_pos_actual_r = Eigen::VectorXd::Zero(7);
    Eigen::VectorXd jnt_pos_actual_l = Eigen::VectorXd::Zero(7);

    Eigen::VectorXd jnt_pos_target_r = Eigen::VectorXd::Zero(7);
    Eigen::VectorXd jnt_pos_target_l = Eigen::VectorXd::Zero(7);

    Eigen::VectorXd motors_current_r = Eigen::VectorXd::Zero(7);
    Eigen::VectorXd motors_current_l = Eigen::VectorXd::Zero(7);

    Eigen::VectorXd ee_vel_teleop = Eigen::VectorXd::Zero(6);

    DynamicArray<std::shared_ptr<coal::ConvexBase>> robotModel_;
    bool is_colli_det_;

    /**
     * @brief 数据发布类的构造函数
     * @details 包含臂手数据发布和数据保存相关的初始化设置
     */
    Arm_Control::Arm_Control(bool flag_colli_det, std::string robot_type){
        nh_ = ros::NodeHandle();

        pub_motor_ = nh_.advertise<std_msgs::Float64MultiArray>("motor_command/arm_position_control", 1);
        pub_rviz_ = nh_.advertise<sensor_msgs::JointState>("joint_states",1000);

        auto now = std::chrono::system_clock::now();
        std::time_t now_c = std::chrono::system_clock::to_time_t(now);
        struct tm now_tm = *std::localtime(&now_c);
        std::string fnPubPos = "./src/robot_controller/data/record/txt/pub_pos/pub_pos_"
                            + std::to_string(now_tm.tm_year + 1900) + "-"
                            + std::to_string(now_tm.tm_mon + 1) + "-"
                            + std::to_string(now_tm.tm_mday) + "-"
                            + std::to_string(now_tm.tm_hour) + "-"
                            + std::to_string(now_tm.tm_min) + "-"
                            + std::to_string(now_tm.tm_sec)
                            + ".txt";
        std::string fnPubVel = "./src/robot_controller/data/record/txt/pub_vel/pub_vel_"
                            + std::to_string(now_tm.tm_year + 1900) + "-"
                            + std::to_string(now_tm.tm_mon + 1) + "-"
                            + std::to_string(now_tm.tm_mday) + "-"
                            + std::to_string(now_tm.tm_hour) + "-"
                            + std::to_string(now_tm.tm_min) + "-"
                            + std::to_string(now_tm.tm_sec)
                            + ".txt";
        std::string fnPubAcc = "./src/robot_controller/data/record/txt/pub_acc/pub_acc_"
                            + std::to_string(now_tm.tm_year + 1900) + "-"
                            + std::to_string(now_tm.tm_mon + 1) + "-"
                            + std::to_string(now_tm.tm_mday) + "-"
                            + std::to_string(now_tm.tm_hour) + "-"
                            + std::to_string(now_tm.tm_min) + "-"
                            + std::to_string(now_tm.tm_sec)
                            + ".txt";
        fwPubPos_=fopen(fnPubPos.c_str(),"w");
        fwPubVel_=fopen(fnPubVel.c_str(),"w");
        fwPubAcc_=fopen(fnPubAcc.c_str(),"w");

        is_colli_det_ = flag_colli_det;
        if(is_colli_det_){
            ANOM_DETEC::AnomalyDetection anomDetec;

            auto start = std::chrono::steady_clock::now();
            robotModel_ = anomDetec.loadRobotModel(robot_type);
            auto end = std::chrono::steady_clock::now();
            auto duration = std::chrono::duration_cast<std::chrono::microseconds>(end-start).count();
            ROS_INFO("The model is successfully resolved[%.5fs]\n", (float)duration/1000000);
        }
    }

    /**
     * @brief 数据发布类的析构函数
     * @details 关闭文件指针，关闭串口
     */
    Arm_Control::~Arm_Control(){
        fclose(fwPubPos_);
        fclose(fwPubVel_);
        fclose(fwPubAcc_);
    }

    /**
     * @brief 数据订阅类的构造函数
     * @details 话题订阅包括对电机位置和目标点位置的订阅，并对电机位置进行数据保存。
     *          其中，话题订阅使用了回调队列，异步接受订阅数据，接收电机位置的回调从初始化已经开始，目标位置的回调需要getCVPos函数控制开始
     */
    Data_Sub::Data_Sub(){
        nh1_ = ros::NodeHandle();
        nh2_ = ros::NodeHandle();
        nh3_ = ros::NodeHandle();
        nh4_ = ros::NodeHandle();

        queue1_ = new ros::CallbackQueue();
        queue2_ = new ros::CallbackQueue();
        queue4_ = new ros::CallbackQueue();

        nh1_.setCallbackQueue(queue1_);
        nh2_.setCallbackQueue(queue2_);
        nh4_.setCallbackQueue(queue4_);

        subActPos_ = nh1_.subscribe("motor_state/arm_motor_state_actual", 1000, &Data_Sub::jointStateCallback, this);
        subCVPos1_ = nh2_.subscribe("/gripper_det_box", 10, &Data_Sub::camera_arrayCallback_base, this);
        subCVPos2_ = nh2_.subscribe("/grasp_point", 10, &Data_Sub::clothes_points_arrayCallback, this);
        subEEVelGoals_ = nh4_.subscribe("relaxed_ik/ee_vel_goals", 10, &Data_Sub::ee_vel_goals_callback, this);

        callService_ = nh3_.serviceClient<robot_controller::ClothePoint>("visual_mission");

        auto now = std::chrono::system_clock::now();
        std::time_t now_c = std::chrono::system_clock::to_time_t(now);
        struct tm now_tm = *std::localtime(&now_c);
        std::string fnSubPos = "./src/robot_controller/data/record/txt/sub_pos/sub_pos_"
                            + std::to_string(now_tm.tm_year + 1900) + "-"
                            + std::to_string(now_tm.tm_mon + 1) + "-"
                            + std::to_string(now_tm.tm_mday) + "-"
                            + std::to_string(now_tm.tm_hour) + "-"
                            + std::to_string(now_tm.tm_min) + "-"
                            + std::to_string(now_tm.tm_sec)
                            + ".txt";
        fwSubPos_=fopen(fnSubPos.c_str(),"w");

        spinner1_ = new ros::AsyncSpinner(1, queue1_);
        spinner2_ = new ros::AsyncSpinner(3, queue2_);
        spinner4_ = new ros::AsyncSpinner(1, queue4_);

        ActPos_flag = false;
        subCV_flag1 = false;
        subCV_flag2 = false;
        
        spinner1_->start();
        spinner4_->start();
    }

    /**
     * @brief 数据订阅类的析构函数
     * @details 关闭回调队列，关闭文件指针
     */
    Data_Sub::~Data_Sub(){
        spinner1_->stop();
        spinner2_->stop();
        spinner4_->start();

        fclose(fwSubPos_);
    }

    /**
     * @brief 数据订阅类的构造函数
     * @details 话题订阅包括对电机位置和目标点位置的订阅，并对电机位置进行数据保存。
     *          其中，话题订阅使用了回调队列，异步接受订阅数据，接收电机位置的回调从初始化已经开始，目标位置的回调需要getCVPos函数控制开始
     */
    Data_Sub_TeleOp::Data_Sub_TeleOp(bool flag_ee_det, std::string arm){
        nh1_ = ros::NodeHandle();
        queue1_ = new ros::CallbackQueue();
        nh1_.setCallbackQueue(queue1_);
        subActPos_ = nh1_.subscribe("motor_state/arm_motor_state_actual", 1000, &Data_Sub_TeleOp::jointStateCallback, this);
        spinner1_ = new ros::AsyncSpinner(1, queue1_);
        ActPos_flag = false;
        spinner1_->start();

        arm_type = arm;

        if(!flag_ee_det){
            nh2_ = ros::NodeHandle();
            queue2_ = new ros::CallbackQueue();
            nh2_.setCallbackQueue(queue2_);

            subCVPos_ = nh2_.subscribe("/dual_arm_hand_joint_positions", 1, &Data_Sub_TeleOp::cvPoseCallback, this);
            // subCVPos1_ = nh2_.subscribe("/cam1_arm_hand_joint_positions", 1, &Data_Sub::mul_cvPoseCallback1, this);
            // subCVPos2_ = nh2_.subscribe("/cam2_arm_hand_joint_positions", 1, &Data_Sub::mul_cvPoseCallback2, this);

            spinner2_ = new ros::AsyncSpinner(3, queue2_);

            subCV_flag = true;
            num_subCV = 0;

            spinner2_->start();
        }else{
            path_package = ros::package::getPath("robot_controller");
            if(arm_type == "gen2"){
                setting_file_path1 = path_package + "/config/relaxed_ik_left_arm_gen2_settings.yaml";
                setting_file_path2 = path_package + "/config/relaxed_ik_right_arm_gen2_settings.yaml";
            }else if(arm_type == "gen3"){
                setting_file_path1 = path_package + "/config/relaxed_ik_left_arm_gen3_settings.yaml";
                setting_file_path2 = path_package + "/config/relaxed_ik_right_arm_gen3_settings.yaml";
            }
            
            left_arm_ik = RelaxedIKRust::initialize(setting_file_path1);
            right_arm_ik = RelaxedIKRust::initialize(setting_file_path2);

            nh2_ = ros::NodeHandle();
            nh3_ = ros::NodeHandle();
            queue2_ = new ros::CallbackQueue();
            queue3_ = new ros::CallbackQueue();
            nh2_.setCallbackQueue(queue2_);
            nh3_.setCallbackQueue(queue3_);

            subEEPoseLeft_ = nh2_.subscribe("/left_hand_pose", 1, &Data_Sub_TeleOp::eePoseCallbackLeft, this);
            subEEPoseRight_ = nh3_.subscribe("/right_hand_pose", 1, &Data_Sub_TeleOp::eePoseCallbackRight, this);
            spinner2_ = new ros::AsyncSpinner(1, queue2_);
            spinner3_ = new ros::AsyncSpinner(1, queue3_);

            spinner2_->start();
            spinner3_->start();
        }

        sec_lpFilter_l.setParam(2,10);
        sec_lpFilter_r.setParam(2,10); 

        double dt = 0.1;
        // state = [x1,dx1,y1,dy1,z1,dz1,x2,dx2,y2,dy2,z2,dz2,x3,dx3,y3,dy3,z3,dz3] 
        int dim_state = 18;
        // measurement = [x1,y1,z1,x2,y2,z2,x3,y3,z3]
        int dim_measure = 9;
        kalmanL.setParam(dim_state,dim_measure,dt); // 18个状态（三个关节点的位置+速度，3*(3+3) = 18），9个观测值（三个关节点的位置）
        kalmanR.setParam(dim_state,dim_measure,dt);
        
        auto now = std::chrono::system_clock::now();
        std::time_t now_c = std::chrono::system_clock::to_time_t(now);
        struct tm now_tm = *std::localtime(&now_c);
        std::string fnSubPos = "./src/robot_controller/data/record/txt/sub_pos/sub_pos_"
                            + std::to_string(now_tm.tm_year + 1900) + "-"
                            + std::to_string(now_tm.tm_mon + 1) + "-"
                            + std::to_string(now_tm.tm_mday) + "-"
                            + std::to_string(now_tm.tm_hour) + "-"
                            + std::to_string(now_tm.tm_min) + "-"
                            + std::to_string(now_tm.tm_sec)
                            + ".txt";
        fwSubPos_=fopen(fnSubPos.c_str(),"w");
    }

    /**
     * @brief 数据订阅类的析构函数
     * @details 关闭回调队列，关闭文件指针
     */
    Data_Sub_TeleOp::~Data_Sub_TeleOp(){
        spinner1_->stop();
        spinner2_->stop();
    }
}