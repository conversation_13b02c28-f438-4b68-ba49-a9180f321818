#!/usr/bin/env python3

import rospy
import rospkg

import numpy as np
from pytransform3d import rotations
from multiprocessing import shared_memory
import time

import os
import sys

from open_television.TeleVisionWrapper import TeleVision<PERSON>rapper
from open_television.DexRetargetWrapper import DexRetargetWrapper
from open_television.exit_utils import setup_signal_handlers, cleanup_resources

from relaxed_ik_solver.arm_gen3_ik_solver import RelaxedIK

# 导入ROS消息类型
from geometry_msgs.msg import PoseStamped, PoseArray, Pose
from std_msgs.msg import Header
from sensor_msgs.msg import JointState
from std_msgs.msg import Int32MultiArray


np.set_printoptions(suppress=True, precision=4)

path_to_src = rospkg.RosPack().get_path('pose_detec_openTeleVision') + '/scripts'

def map_value(x, in_min=0, in_max=np.deg2rad(98), out_min=1000, out_max=0):
    if x<in_min:
        x = in_min
    elif x>in_max:
        x = in_max
    # 线性插值计算映射值
    return int((x - in_min) * (out_max - out_min) / (in_max - in_min) + out_min)

# print(map_value(np.deg2rad(28.6479),np.deg2rad(0),np.deg2rad(15),1000,0))

if __name__ == '__main__':
    rospy.init_node('teleop_node')

    """
    主程序入口：创建远程操作和模拟环境，并运行主循环
    """
    current_dir = os.path.dirname(os.path.abspath(__file__))
    print("current_dir: ", current_dir)
    sys.path.append(current_dir)

    # 初始化远程操作器和模拟器
    # 创建图像相关资源
    img_shape = (480, 640, 3)  # 图像形状：高度720，宽度1280 (640*2)，3个颜色通道
    # 创建共享内存用于存储图像数据
    img_shm = shared_memory.SharedMemory(create=True, size=np.prod(img_shape) * np.uint8().itemsize)

    cart_path = path_to_src + "/open_television/pem/cert.pem"
    key_path = path_to_src + "/open_television/pem/key.pem"

    # 如果需要使用真实的TeleVisionWrapper和DexRetargetWrapper，请使用以下代码
    teleoperator = TeleVisionWrapper(False, img_shape, img_shm.name, cart_path, key_path)
    retargeter = DexRetargetWrapper(path_to_src + '/open_television/configs/inspire_hand1.yml')

    # 创建RelaxedIK求解器实例
    relaxed_ik_R = RelaxedIK('right')
    relaxed_ik_L = RelaxedIK('left')
    defult_tolerances = [0.0] * 6

    # 创建ROS发布器，用于发布左右手位姿数据
    left_pose_pub = rospy.Publisher('/left_hand_pose', PoseStamped, queue_size=10)
    right_pose_pub = rospy.Publisher('/right_hand_pose', PoseStamped, queue_size=10)

    # 创建关节状态发布器，用于发布机器人关节角度
    joint_state_pub = rospy.Publisher('/joint_states', JointState, queue_size=10)

    # 创建左右手手指关节状态发布器
    left_finger_joint_pub = rospy.Publisher('/chatter1', Int32MultiArray, queue_size=10)
    right_finger_joint_pub = rospy.Publisher('/chatter1_right', Int32MultiArray, queue_size=10)

    # 定义关节名称列表，确保与URDF中的关节名称一致
    joint_names = [
        # 左臂关节
        'AJL1', 'AJL2', 'AJL3', 'AJL4', 'AJL5', 'AJL6', 'AJL7',
        # 右臂关节
        'AJR1', 'AJR2', 'AJR3', 'AJR4', 'AJR5', 'AJR6', 'AJR7'
    ]

    # 创建一个共享的关闭标志
    shutdown_requested = [False]

    # 设置信号处理器
    setup_signal_handlers(shutdown_requested)

    try:
        # 主循环
        while not rospy.is_shutdown() and not shutdown_requested[0]:
            head_mat, armgen3_left_wrist, armgen3_right_wrist, inspire_left_hand, inspire_right_hand = teleoperator.get_data()

            # 计算左手位姿（位置和四元数）
            left_pose = np.concatenate([armgen3_left_wrist[:3, 3] + np.array([0, 0, 0]),  # 位置偏移
                                        rotations.quaternion_from_matrix(armgen3_left_wrist[:3, :3])[[1, 2, 3, 0]]])  # 旋转四元数
            # 计算右手位姿（位置和四元数）
            right_pose = np.concatenate([armgen3_right_wrist[:3, 3] + np.array([-0.0, 0, 0.0]),  # 位置偏移
                                        rotations.quaternion_from_matrix(armgen3_right_wrist[:3, :3])[[1, 2, 3, 0]]])  # 旋转四元数

            # 使用重定向模型计算左手关节角度并重新排序
            # ┌──────┬───────┬──────┬────────┬────────┬────────────┬────────────────┐
            # │ Id   │   4   │  6   │   2    │   0    │     8      │       9        │
            # ├──────┼───────┼──────┼────────┼────────┼────────────┼────────────────┤
            # │Joint │ pinky │ ring │ middle │ index  │ thumb-bend │ thumb-rotation │
            # └──────┴───────┴──────┴────────┴────────┴────────────┴────────────────┘
            qpos_fingers_left, qpos_fingers_right = retargeter.retarget(inspire_left_hand, inspire_right_hand)
            # print("qpos_fingers_left: ")
            # print(np.rad2deg(qpos_fingers_left))
            # qpos_left = np.concatenate([qpos_fingers_left[[4, 6, 2, 0, 8, 9]]])
            # qpos_right = np.concatenate([qpos_fingers_right[[4, 6, 2, 0, 8, 9]]])

            left_fingers_msgs = Int32MultiArray()
            # left_fingers_msgs.data = [
            #     map_value(qpos_left[0]),
            #     map_value(qpos_left[1]),
            #     map_value(qpos_left[2]),
            #     map_value(qpos_left[3]),
            #     map_value(qpos_left[5], 0, np.deg2rad(15), 1000, 0),
            #     map_value(qpos_left[4], np.deg2rad(-6), np.deg2rad(75), 1000, 0)
            # ]
            left_fingers_msgs.data = [
                map_value(qpos_fingers_left[0],out_min=0,out_max=2000),
                map_value(qpos_fingers_left[1],out_min=0,out_max=2000),
                map_value(qpos_fingers_left[2],out_min=0,out_max=2000),
                map_value(qpos_fingers_left[3],out_min=0,out_max=2000),
                map_value(qpos_fingers_left[4], 0, np.deg2rad(15), 0, 2000),
                map_value(qpos_fingers_left[5], np.deg2rad(-6), np.deg2rad(75), 0, 2000)
            ]
            if qpos_fingers_left[5] > np.deg2rad(20):
                left_fingers_msgs.data[4] = 800
                left_fingers_msgs.data[5] = 2000
            if qpos_fingers_left[3] > np.deg2rad(25):
                left_fingers_msgs.data[3] = 1220

            # right_fingers_msgs = Int32MultiArray()
            # right_fingers_msgs.data = [
            #     map_value(qpos_right[0]),
            #     map_value(qpos_right[1]),
            #     map_value(qpos_right[2]),
            #     map_value(qpos_right[3]),
            #     map_value(qpos_right[5], 0, np.deg2rad(15), 1000, 0),
            #     map_value(qpos_right[4], -np.deg2rad(6), np.deg2rad(75), 1000, 0)
            # ]
            # right_fingers_msgs.data = [
            #     map_value(qpos_right[0],out_min=0,out_max=2000),
            #     map_value(qpos_right[1],out_min=0,out_max=2000),
            #     map_value(qpos_right[2],out_min=0,out_max=2000),
            #     map_value(qpos_right[3],out_min=0,out_max=2000),
            #     map_value(qpos_right[5], 0, np.deg2rad(15), 0, 2000),
            #     map_value(qpos_right[4], np.deg2rad(-6), np.deg2rad(75), 0, 2000)
            # ]

            print("qpos_left: ")
            print(np.rad2deg(qpos_fingers_left))
            print("left_fingers_msgs: ")
            print(left_fingers_msgs.data)

            left_finger_joint_pub.publish(left_fingers_msgs)

            # 创建并发布左手位姿消息
            left_pose_msg = PoseStamped()
            left_pose_msg.header.stamp = rospy.Time.now()
            left_pose_msg.header.frame_id = "world"

            # 设置位置
            left_pose_msg.pose.position.x = left_pose[0]
            left_pose_msg.pose.position.y = left_pose[1]
            left_pose_msg.pose.position.z = left_pose[2]

            # 设置方向（四元数）
            left_pose_msg.pose.orientation.x = left_pose[3]
            left_pose_msg.pose.orientation.y = left_pose[4]
            left_pose_msg.pose.orientation.z = left_pose[5]
            left_pose_msg.pose.orientation.w = left_pose[6]

            # 创建并发布右手位姿消息
            right_pose_msg = PoseStamped()
            right_pose_msg.header.stamp = rospy.Time.now()
            right_pose_msg.header.frame_id = "world"

            # 设置位置
            right_pose_msg.pose.position.x = right_pose[0]
            right_pose_msg.pose.position.y = right_pose[1]
            right_pose_msg.pose.position.z = right_pose[2]

            # 设置方向（四元数）
            right_pose_msg.pose.orientation.x = right_pose[3]
            right_pose_msg.pose.orientation.y = right_pose[4]
            right_pose_msg.pose.orientation.z = right_pose[5]
            right_pose_msg.pose.orientation.w = right_pose[6]

            # 发布位姿消息
            left_pose_pub.publish(left_pose_msg)
            right_pose_pub.publish(right_pose_msg)


            # print("left_pose: ")
            # print(left_pose)
            # print("right_pose: ")
            # print(right_pose)
            # print("left_finger_joints: ")
            # print(qpos_left)
            # print("right_finger_joints: ")
            # print(qpos_right)

            # # 注意：这里需要确保左右臂的求解器使用正确
            # # 左臂使用left求解器，右臂使用right求解器
            # qpos_arm_left = relaxed_ik_L.relaxed_ik.solve_position(
            #     [left_pose[0], left_pose[1], left_pose[2]], [left_pose[3], left_pose[4], left_pose[5], left_pose[6]], defult_tolerances
            # )
            # qpos_arm_right = relaxed_ik_R.relaxed_ik.solve_position(
            #     [right_pose[0], right_pose[1], right_pose[2]], [right_pose[3], right_pose[4], right_pose[5], right_pose[6]], defult_tolerances
            # )
            # print("qpos_arm_left: ")
            # print(qpos_arm_left)
            # print("qpos_arm_right: ")
            # print(qpos_arm_right)

            # # 创建并发布关节状态消息
            # joint_state_msg = JointState()
            # joint_state_msg.header.stamp = rospy.Time.now()
            # joint_state_msg.name = joint_names

            # # 合并左右臂关节角度（注意：这里需要确保顺序与joint_names一致）
            # # 左臂关节在前，右臂关节在后
            # # 注意：根据URDF文件，左臂关节是AJL1-AJL7，右臂关节是AJR1-AJR7
            # joint_positions = np.concatenate([qpos_arm_left, qpos_arm_right])
            # joint_state_msg.position = joint_positions.tolist()

            # # 添加速度和力矩字段（可选，设为空）
            # joint_state_msg.velocity = []
            # joint_state_msg.effort = []

            # # 发布关节状态
            # joint_state_pub.publish(joint_state_msg)
            # rospy.loginfo_throttle(1.0, "已发布关节状态")

            time.sleep(0.03)  # 约33Hz的更新频率
    except KeyboardInterrupt:
        print("\n接收到Ctrl+C，正在关闭程序...")
        shutdown_requested[0] = True
    except Exception as e:
        print(f"\n程序发生异常: {e}")
        shutdown_requested[0] = True
    finally:
        # 使用exit_utils中的cleanup_resources函数清理资源
        cleanup_resources(teleoperator, img_shm)
        exit(0)
