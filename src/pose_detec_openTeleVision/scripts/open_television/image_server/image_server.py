import cv2
import zmq
import time
import struct
from collections import deque
import numpy as np
import pyrealsense2 as rs


class RealSenseCamera(object):
    def __init__(self, img_shape, fps, serial_number=None, enable_depth=False) -> None:
        """
        初始化RealSense相机对象

        参数:
        img_shape: [height, width] - 图像分辨率
        fps: 帧率
        serial_number: 相机序列号，如果为None则使用第一个可用设备
        enable_depth: 是否启用深度流
        """
        self.img_shape = img_shape
        self.fps = fps
        self.serial_number = serial_number
        self.enable_depth = enable_depth
        self.initialized = False

        # 创建对齐对象，用于将深度帧与彩色帧对齐
        align_to = rs.stream.color
        self.align = rs.align(align_to)

        # 初始化相机
        if not self.init_realsense():
            print(f"[Image Server] 警告: RealSense相机初始化失败，尝试使用更小的分辨率")
            # 尝试使用更小的分辨率
            self.img_shape = [480, 640]
            if not self.init_realsense():
                print(f"[Image Server] 错误: RealSense相机初始化失败，无法继续")
                raise RuntimeError("无法初始化RealSense相机")

    def init_realsense(self):
        """初始化RealSense相机"""
        try:
            # 检查可用设备
            ctx = rs.context()
            devices = ctx.query_devices()
            if len(devices) == 0:
                print("[Image Server] 错误: 未检测到RealSense相机设备")
                return False

            # 如果指定了序列号，检查该设备是否存在
            if self.serial_number is not None:
                device_found = False
                for dev in devices:
                    if dev.get_info(rs.camera_info.serial_number) == self.serial_number:
                        device_found = True
                        break
                if not device_found:
                    print(f"[Image Server] 错误: 未找到序列号为 {self.serial_number} 的设备")
                    print("[Image Server] 可用设备序列号:")
                    for dev in devices:
                        print(f"  - {dev.get_info(rs.camera_info.serial_number)}")
                    return False

            # 创建管道和配置
            self.pipeline = rs.pipeline()
            config = rs.config()

            # 如果指定了序列号，启用该设备
            if self.serial_number is not None:
                config.enable_device(self.serial_number)

            # 尝试使用请求的分辨率和帧率
            try:
                # 配置彩色流，确保使用bgr8格式
                config.enable_stream(rs.stream.color, self.img_shape[1], self.img_shape[0], rs.format.bgr8, self.fps)

                if self.enable_depth:
                    config.enable_stream(rs.stream.depth, self.img_shape[1], self.img_shape[0], rs.format.z16, self.fps)

                # 启动管道
                profile = self.pipeline.start(config)

                # 获取彩色传感器并设置高级选项
                color_sensor = profile.get_device().first_color_sensor()
                if color_sensor:
                    # 启用自动曝光
                    color_sensor.set_option(rs.option.enable_auto_exposure, 1)
                    # 设置自动白平衡
                    if color_sensor.supports(rs.option.enable_auto_white_balance):
                        color_sensor.set_option(rs.option.enable_auto_white_balance, 1)
                    # 设置色彩增强
                    if color_sensor.supports(rs.option.saturation):
                        color_sensor.set_option(rs.option.saturation, 65)  # 增强色彩饱和度
                    # 设置锐度
                    if color_sensor.supports(rs.option.sharpness):
                        color_sensor.set_option(rs.option.sharpness, 50)  # 适中的锐度

            except RuntimeError as e:
                print(f"[Image Server] 错误: 无法使用请求的配置: {e}")
                print("[Image Server] 尝试使用默认配置...")

                # 重置配置
                config = rs.config()
                if self.serial_number is not None:
                    config.enable_device(self.serial_number)

                # 使用默认配置
                profile = self.pipeline.start(config)
                print("[Image Server] 成功使用默认配置启动相机")

            # 获取设备信息
            self._device = profile.get_device()
            if self._device is None:
                print('[Image Server] pipe_profile.get_device() is None.')
                return False

            if self.enable_depth:
                depth_sensor = self._device.first_depth_sensor()
                self.g_depth_scale = depth_sensor.get_depth_scale()

            # 获取内参
            self.intrinsics = profile.get_stream(rs.stream.color).as_video_stream_profile().get_intrinsics()

            # 打印相机信息
            print(f"[Image Server] 成功初始化RealSense相机")
            print(f"[Image Server] 相机序列号: {self._device.get_info(rs.camera_info.serial_number)}")
            print(f"[Image Server] 相机名称: {self._device.get_info(rs.camera_info.name)}")

            return True

        except Exception as e:
            print(f"[Image Server] 初始化RealSense相机时发生错误: {e}")
            return False

    def get_frame(self):
        """
        获取相机帧

        返回:
        如果成功: 彩色图像和深度图像（如果启用）
        如果失败: (None, None)
        """
        try:
            # 等待帧
            frames = self.pipeline.wait_for_frames()
            # 对齐帧
            aligned_frames = self.align.process(frames)
            # 获取彩色帧
            color_frame = aligned_frames.get_color_frame()

            # 如果启用深度，获取深度帧
            depth_frame = None
            if self.enable_depth:
                depth_frame = aligned_frames.get_depth_frame()

            # 检查彩色帧是否有效
            if not color_frame:
                print("[Image Server] 无效的彩色帧")
                return None, None

            # 将帧转换为numpy数组
            color_image = np.asanyarray(color_frame.get_data())

            # 如果启用深度，将深度帧转换为numpy数组
            depth_image = None
            if self.enable_depth and depth_frame:
                depth_image = np.asanyarray(depth_frame.get_data())

            return color_image, depth_image

        except Exception as e:
            print(f"[Image Server] 获取帧时发生错误: {e}")
            return None, None

    def release(self):
        """释放相机资源"""
        try:
            # 检查pipeline是否已初始化并且可以安全地停止
            if hasattr(self, 'pipeline') and self.pipeline:
                # 检查pipeline是否已经启动
                try:
                    # 尝试获取一帧，如果成功，说明pipeline已启动
                    self.pipeline.wait_for_frames(timeout_ms=100)
                    # 如果没有抛出异常，则停止pipeline
                    self.pipeline.stop()
                    print(f"[Image Server] RealSense相机已释放")
                except Exception:
                    # 如果获取帧失败，可能pipeline尚未启动，跳过停止操作
                    print(f"[Image Server] RealSense相机pipeline未启动，跳过释放")
        except Exception as e:
            print(f"[Image Server] 释放RealSense相机时发生错误: {e}")


class OpenCVCamera():
    def __init__(self, device_id, img_shape, fps):
        """
        decive_id: /dev/video* or *
        img_shape: [height, width]
        """
        self.id = device_id
        self.fps = fps
        self.img_shape = img_shape
        self.cap = cv2.VideoCapture(self.id, cv2.CAP_V4L2)
        self.cap.set(cv2.CAP_PROP_FOURCC, cv2.VideoWriter.fourcc('M', 'J', 'P', 'G'))
        self.cap.set(cv2.CAP_PROP_FRAME_HEIGHT, self.img_shape[0])
        self.cap.set(cv2.CAP_PROP_FRAME_WIDTH,  self.img_shape[1])
        self.cap.set(cv2.CAP_PROP_FPS, self.fps)

        # Test if the camera can read frames
        if not self._can_read_frame():
            print(f"[Image Server] Camera {self.id} Error: Failed to initialize the camera or read frames. Exiting...")
            self.release()

    def _can_read_frame(self):
        success, _ = self.cap.read()
        return success

    def release(self):
        """释放相机资源"""
        try:
            self.cap.release()
            print(f"[Image Server] OpenCV相机 {self.id} 已释放")
        except Exception as e:
            print(f"[Image Server] 释放OpenCV相机 {self.id} 时发生错误: {e}")

    def get_frame(self):
        ret, color_image = self.cap.read()
        if not ret:
            return None
        return color_image


class ImageServer:
    def __init__(self, config, port = 5555, Unit_Test = False):
        """
        config example1:
        {
            'fps':30                                                          # frame per second
            'head_camera_type': 'opencv',                                     # opencv or realsense
            'head_camera_image_shape': [480, 1280],                           # Head camera resolution  [height, width]
            'head_camera_id_numbers': [0],                                    # '/dev/video0' (opencv)
            'wrist_camera_type': 'realsense',
            'wrist_camera_image_shape': [480, 640],                           # Wrist camera resolution  [height, width]
            'wrist_camera_id_numbers': ["218622271789", "241222076627"],      # realsense camera's serial number
        }

        config example2:
        {
            'fps':30                                                          # frame per second
            'head_camera_type': 'realsense',                                  # opencv or realsense
            'head_camera_image_shape': [480, 640],                            # Head camera resolution  [height, width]
            'head_camera_id_numbers': ["218622271739"],                       # realsense camera's serial number
            'wrist_camera_type': 'opencv',
            'wrist_camera_image_shape': [480, 640],                           # Wrist camera resolution  [height, width]
            'wrist_camera_id_numbers': [0,1],                                 # '/dev/video0' and '/dev/video1' (opencv)
        }

        If you are not using the wrist camera, you can comment out its configuration, like this below:
        config:
        {
            'fps':30                                                          # frame per second
            'head_camera_type': 'opencv',                                     # opencv or realsense
            'head_camera_image_shape': [480, 1280],                           # Head camera resolution  [height, width]
            'head_camera_id_numbers': [0],                                    # '/dev/video0' (opencv)
            #'wrist_camera_type': 'realsense',
            #'wrist_camera_image_shape': [480, 640],                           # Wrist camera resolution  [height, width]
            #'wrist_camera_id_numbers': ["218622271789", "241222076627"],      # serial number (realsense)
        }
        """
        print(config)
        self.fps = config.get('fps', 30)
        self.head_camera_type = config.get('head_camera_type', 'opencv')
        self.head_image_shape = config.get('head_camera_image_shape', [480, 640])      # (height, width)
        self.head_camera_id_numbers = config.get('head_camera_id_numbers', [0])

        self.wrist_camera_type = config.get('wrist_camera_type', None)
        self.wrist_image_shape = config.get('wrist_camera_image_shape', [480, 640])    # (height, width)
        self.wrist_camera_id_numbers = config.get('wrist_camera_id_numbers', None)

        self.port = port
        self.Unit_Test = Unit_Test


        # Initialize head cameras
        self.head_cameras = []
        if self.head_camera_type == 'opencv':
            for device_id in self.head_camera_id_numbers:
                camera = OpenCVCamera(device_id=device_id, img_shape=self.head_image_shape, fps=self.fps)
                self.head_cameras.append(camera)
        elif self.head_camera_type == 'realsense':
            for serial_number in self.head_camera_id_numbers:
                camera = RealSenseCamera(img_shape=self.head_image_shape, fps=self.fps, serial_number=serial_number)
                self.head_cameras.append(camera)
        else:
            print(f"[Image Server] Unsupported head_camera_type: {self.head_camera_type}")

        # Initialize wrist cameras if provided
        self.wrist_cameras = []
        if self.wrist_camera_type and self.wrist_camera_id_numbers:
            if self.wrist_camera_type == 'opencv':
                for device_id in self.wrist_camera_id_numbers:
                    camera = OpenCVCamera(device_id=device_id, img_shape=self.wrist_image_shape, fps=self.fps)
                    self.wrist_cameras.append(camera)
            elif self.wrist_camera_type == 'realsense':
                for serial_number in self.wrist_camera_id_numbers:
                    camera = RealSenseCamera(img_shape=self.wrist_image_shape, fps=self.fps, serial_number=serial_number)
                    self.wrist_cameras.append(camera)
            else:
                print(f"[Image Server] Unsupported wrist_camera_type: {self.wrist_camera_type}")

        # Set ZeroMQ context and socket
        self.context = zmq.Context()
        self.socket = self.context.socket(zmq.PUB)
        self.socket.bind(f"tcp://*:{self.port}")

        if self.Unit_Test:
            self._init_performance_metrics()

        for cam in self.head_cameras:
            if isinstance(cam, OpenCVCamera):
                print(f"[Image Server] Head camera {cam.id} resolution: {cam.cap.get(cv2.CAP_PROP_FRAME_HEIGHT)} x {cam.cap.get(cv2.CAP_PROP_FRAME_WIDTH)}")
            elif isinstance(cam, RealSenseCamera):
                print(f"[Image Server] Head camera {cam.serial_number} resolution: {cam.img_shape[0]} x {cam.img_shape[1]}")
            else:
                print("[Image Server] Unknown camera type in head_cameras.")

        for cam in self.wrist_cameras:
            if isinstance(cam, OpenCVCamera):
                print(f"[Image Server] Wrist camera {cam.id} resolution: {cam.cap.get(cv2.CAP_PROP_FRAME_HEIGHT)} x {cam.cap.get(cv2.CAP_PROP_FRAME_WIDTH)}")
            elif isinstance(cam, RealSenseCamera):
                print(f"[Image Server] Wrist camera {cam.serial_number} resolution: {cam.img_shape[0]} x {cam.img_shape[1]}")
            else:
                print("[Image Server] Unknown camera type in wrist_cameras.")

        print("[Image Server] Image server has started, waiting for client connections...")



    def _init_performance_metrics(self):
        self.frame_count = 0  # Total frames sent
        self.time_window = 1.0  # Time window for FPS calculation (in seconds)
        self.frame_times = deque()  # Timestamps of frames sent within the time window
        self.start_time = time.time()  # Start time of the streaming

    def _update_performance_metrics(self, current_time):
        # Add current time to frame times deque
        self.frame_times.append(current_time)
        # Remove timestamps outside the time window
        while self.frame_times and self.frame_times[0] < current_time - self.time_window:
            self.frame_times.popleft()
        # Increment frame count
        self.frame_count += 1

    def _print_performance_metrics(self, current_time):
        if self.frame_count % 30 == 0:
            elapsed_time = current_time - self.start_time
            real_time_fps = len(self.frame_times) / self.time_window
            print(f"[Image Server] Real-time FPS: {real_time_fps:.2f}, Total frames sent: {self.frame_count}, Elapsed time: {elapsed_time:.2f} sec")

    def _close(self):
        """
        关闭图像服务器并释放所有资源

        安全地关闭所有相机连接和ZMQ通信
        """
        # 防止重复关闭
        if hasattr(self, '_closed') and self._closed:
            print("[Image Server] 图像服务器已经关闭，跳过重复关闭")
            return

        print("[Image Server] 正在关闭图像服务器...")

        # 标记为已关闭
        self._closed = True

        # 设置运行标志为False，确保发送循环退出
        if hasattr(self, 'running'):
            self.running = False

        # 关闭头部相机
        for cam in self.head_cameras:
            try:
                cam.release()
            except Exception as e:
                print(f"[Image Server] 关闭头部相机时发生错误: {e}")

        # 关闭腕部相机
        for cam in self.wrist_cameras:
            try:
                cam.release()
            except Exception as e:
                print(f"[Image Server] 关闭腕部相机时发生错误: {e}")

        # 关闭ZMQ通信
        try:
            if hasattr(self, 'socket') and self.socket:
                self.socket.close()
                print("[Image Server] ZMQ套接字已关闭")
        except Exception as e:
            print(f"[Image Server] 关闭ZMQ套接字时发生错误: {e}")

        try:
            if hasattr(self, 'context') and self.context:
                self.context.term()
                print("[Image Server] ZMQ上下文已终止")
        except Exception as e:
            print(f"[Image Server] 终止ZMQ上下文时发生错误: {e}")

        print("[Image Server] 图像服务器已完全关闭")

    def send_process(self):
        """
        图像发送主循环

        从相机获取图像，处理后通过ZMQ发送
        """
        # 添加一个标志来控制循环
        self.running = True

        try:
            while self.running:
                try:
                    # 获取头部相机图像
                    head_frames = []
                    for cam in self.head_cameras:
                        if self.head_camera_type == 'opencv':
                            color_image = cam.get_frame()
                            if color_image is None:
                                print("[Image Server] Head camera frame read is error.")
                                break
                        elif self.head_camera_type == 'realsense':
                            # 获取彩色图像和深度图像（如果启用）
                            color_image, _ = cam.get_frame()  # 忽略深度图像
                            if color_image is None:
                                print("[Image Server] Head camera frame read is error.")
                                break
                        head_frames.append(color_image)

                    # 检查是否所有头部相机都成功获取了图像
                    if len(head_frames) != len(self.head_cameras):
                        print("[Image Server] 未能从所有头部相机获取图像，跳过当前帧")
                        time.sleep(0.01)  # 短暂暂停，避免CPU占用过高
                        continue

                    # 合并头部相机图像
                    head_color = cv2.hconcat(head_frames)

                    # 处理腕部相机图像（如果有）
                    if self.wrist_cameras:
                        wrist_frames = []
                        for cam in self.wrist_cameras:
                            if self.wrist_camera_type == 'opencv':
                                color_image = cam.get_frame()
                                if color_image is None:
                                    print("[Image Server] Wrist camera frame read is error.")
                                    break
                            elif self.wrist_camera_type == 'realsense':
                                # 获取彩色图像和深度图像（如果启用）
                                color_image, _ = cam.get_frame()  # 忽略深度图像
                                if color_image is None:
                                    print("[Image Server] Wrist camera frame read is error.")
                                    break
                            wrist_frames.append(color_image)

                        # 检查是否所有腕部相机都成功获取了图像
                        if len(wrist_frames) != len(self.wrist_cameras):
                            print("[Image Server] 未能从所有腕部相机获取图像，跳过当前帧")
                            time.sleep(0.01)  # 短暂暂停，避免CPU占用过高
                            continue

                        # 合并腕部相机图像
                        wrist_color = cv2.hconcat(wrist_frames)

                        # 合并头部和腕部图像
                        full_color = cv2.hconcat([head_color, wrist_color])
                    else:
                        full_color = head_color

                    # 编码图像为JPEG格式，提高质量并保持颜色准确性
                    encode_params = [cv2.IMWRITE_JPEG_QUALITY, 95]  # 设置JPEG质量为95%
                    ret, buffer = cv2.imencode('.jpg', full_color, encode_params)
                    if not ret:
                        print("[Image Server] 图像编码失败")
                        continue

                    # 转换为字节流
                    jpg_bytes = buffer.tobytes()

                    # 如果是性能测试模式，添加时间戳和帧ID
                    if self.Unit_Test:
                        timestamp = time.time()
                        frame_id = self.frame_count
                        header = struct.pack('dI', timestamp, frame_id)  # 8-byte double, 4-byte unsigned int
                        message = header + jpg_bytes
                    else:
                        message = jpg_bytes

                    # 发送图像数据
                    self.socket.send(message)

                    # 如果是性能测试模式，更新和打印性能指标
                    if self.Unit_Test:
                        current_time = time.time()
                        self._update_performance_metrics(current_time)
                        self._print_performance_metrics(current_time)

                except Exception as e:
                    print(f"[Image Server] 处理图像帧时发生错误: {e}")
                    time.sleep(0.1)  # 出错时暂停一下，避免错误循环消耗资源

        except KeyboardInterrupt:
            print("[Image Server] 用户中断")
        except Exception as e:
            print(f"[Image Server] 图像服务器主循环发生错误: {e}")
        finally:
            # 确保在退出时关闭所有资源
            self.running = False
            self._close()


if __name__ == "__main__":
    config = {
        'fps': 30,
        'head_camera_type': 'realsense',
        'head_camera_image_shape': [480,640],  # 提高分辨率到HD (720p)
        'head_camera_id_numbers': ["335222076022"],
        # 'wrist_camera_type': 'opencv',
        # 'wrist_camera_image_shape': [720, 1280],  # 同样提高腕部相机分辨率
        # 'wrist_camera_id_numbers': [2, 4],
    }

    server = ImageServer(config, Unit_Test=False)
    server.send_process()