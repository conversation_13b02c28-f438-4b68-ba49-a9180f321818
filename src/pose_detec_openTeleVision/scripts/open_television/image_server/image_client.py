# 导入必要的库
import cv2          # 用于图像处理和显示
import zmq          # ZeroMQ库，用于网络通信
import numpy as np  # 用于数组操作和图像处理
import time         # 用于时间戳和性能测量
import struct       # 用于二进制数据的打包和解包
from collections import deque  # 用于实现滑动窗口计算性能指标
from multiprocessing import shared_memory  # 用于进程间共享图像数据

class ImageClient:
    """
    图像客户端类，用于从服务器接收图像数据并处理

    该类通过ZeroMQ订阅模式从图像服务器接收图像数据，支持共享内存方式传输图像到其他进程，
    并可选择性地显示接收到的图像和评估传输性能。
    """
    def __init__(self, tv_img_shape = None, tv_img_shm_name = None, wrist_img_shape = None, wrist_img_shm_name = None,
                       image_show = False, server_address = "*************", port = 8080, Unit_Test = False):
        """
        初始化图像客户端

        参数:
        tv_img_shape: 头部摄像头图像的预期分辨率形状 (高, 宽, 通道数)。应与图像服务终端的输出匹配。

        tv_img_shm_name: 共享内存名称，用于在进程间轻松传输图像到Vuer。

        wrist_img_shape: 腕部摄像头图像的预期分辨率形状 (高, 宽, 通道数)。应与tv_img_shape保持相同形状。

        wrist_img_shm_name: 共享内存名称，用于传输腕部摄像头图像。

        image_show: 是否实时显示接收到的图像。

        server_address: 执行图像服务器脚本的IP地址。

        port: 要绑定的端口号。应与图像服务器相同。

        Unit_Test: 当设置为True时，可用于测试图像传输延迟、网络抖动、帧丢失率等性能信息。
        """
        # 控制客户端运行状态的标志
        self.running = True
        # 是否显示接收到的图像
        self._image_show = image_show
        # 服务器地址和端口
        self._server_address = server_address
        self._port = port

        # 存储图像形状信息
        self.tv_img_shape = tv_img_shape
        self.wrist_img_shape = wrist_img_shape

        # 初始化头部摄像头共享内存
        self.tv_enable_shm = False
        if self.tv_img_shape is not None and tv_img_shm_name is not None:
            # 连接到已创建的共享内存
            self.tv_image_shm = shared_memory.SharedMemory(name=tv_img_shm_name)
            # 创建numpy数组，使用共享内存作为缓冲区
            self.tv_img_array = np.ndarray(tv_img_shape, dtype = np.uint8, buffer = self.tv_image_shm.buf)
            self.tv_enable_shm = True

        # 初始化腕部摄像头共享内存
        self.wrist_enable_shm = False
        if self.wrist_img_shape is not None and wrist_img_shm_name is not None:
            # 连接到已创建的共享内存
            self.wrist_image_shm = shared_memory.SharedMemory(name=wrist_img_shm_name)
            # 创建numpy数组，使用共享内存作为缓冲区
            self.wrist_img_array = np.ndarray(wrist_img_shape, dtype = np.uint8, buffer = self.wrist_image_shm.buf)
            self.wrist_enable_shm = True

        # 性能评估参数
        self._enable_performance_eval = Unit_Test
        if self._enable_performance_eval:
            # 如果启用性能评估，初始化相关指标
            self._init_performance_metrics()

    def _init_performance_metrics(self):
        """
        初始化性能评估指标

        设置用于跟踪和计算图像传输性能的各种计数器和数据结构
        """
        self._frame_count = 0  # 接收到的总帧数
        self._last_frame_id = -1  # 上一个接收到的帧ID

        # 使用时间窗口计算实时FPS
        self._time_window = 1.0  # 时间窗口大小（秒）
        self._frame_times = deque()  # 时间窗口内接收到的帧的时间戳队列

        # 数据传输质量指标
        self._latencies = deque()  # 时间窗口内帧的延迟队列
        self._lost_frames = 0  # 丢失的总帧数
        self._total_frames = 0  # 基于帧ID的预期总帧数

    def _update_performance_metrics(self, timestamp, frame_id, receive_time):
        """
        更新性能评估指标

        参数:
        timestamp: 图像在服务器端的时间戳
        frame_id: 图像帧的ID
        receive_time: 客户端接收到图像的时间戳
        """
        # 计算延迟（接收时间 - 发送时间）
        latency = receive_time - timestamp
        self._latencies.append(latency)

        # 移除时间窗口外的延迟数据
        while self._latencies and self._frame_times and self._latencies[0] < receive_time - self._time_window:
            self._latencies.popleft()

        # 更新帧时间
        self._frame_times.append(receive_time)
        # 移除时间窗口外的时间戳
        while self._frame_times and self._frame_times[0] < receive_time - self._time_window:
            self._frame_times.popleft()

        # 更新帧计数，用于丢帧计算
        expected_frame_id = self._last_frame_id + 1 if self._last_frame_id != -1 else frame_id
        if frame_id != expected_frame_id:
            lost = frame_id - expected_frame_id
            if lost < 0:
                # 接收到了乱序的帧
                print(f"[Image Client] Received out-of-order frame ID: {frame_id}")
            else:
                # 检测到丢帧
                self._lost_frames += lost
                print(f"[Image Client] Detected lost frames: {lost}, Expected frame ID: {expected_frame_id}, Received frame ID: {frame_id}")
        self._last_frame_id = frame_id
        self._total_frames = frame_id + 1

        # 增加接收到的帧计数
        self._frame_count += 1

    def _print_performance_metrics(self, receive_time):
        """
        打印性能评估指标

        每接收30帧后打印一次性能指标，包括FPS、延迟和丢帧率

        参数:
        receive_time: 当前帧接收时间，用于计算时间窗口内的指标
        """
        if self._frame_count % 30 == 0:  # 每30帧打印一次性能指标
            # 计算实时FPS（时间窗口内的帧数/时间窗口大小）
            real_time_fps = len(self._frame_times) / self._time_window if self._time_window > 0 else 0

            # 计算延迟指标
            if self._latencies:
                avg_latency = sum(self._latencies) / len(self._latencies)  # 平均延迟
                max_latency = max(self._latencies)  # 最大延迟
                min_latency = min(self._latencies)  # 最小延迟
                jitter = max_latency - min_latency  # 网络抖动（最大延迟-最小延迟）
            else:
                avg_latency = max_latency = min_latency = jitter = 0

            # 计算丢帧率（丢失的帧数/总帧数）
            lost_frame_rate = (self._lost_frames / self._total_frames) * 100 if self._total_frames > 0 else 0

            # 打印性能指标
            print(f"[Image Client] Real-time FPS: {real_time_fps:.2f}, Avg Latency: {avg_latency*1000:.2f} ms, Max Latency: {max_latency*1000:.2f} ms, \
                  Min Latency: {min_latency*1000:.2f} ms, Jitter: {jitter*1000:.2f} ms, Lost Frame Rate: {lost_frame_rate:.2f}%")

    def _close(self):
        """
        关闭客户端

        安全地关闭ZMQ套接字和上下文，释放共享内存，如果启用了图像显示则关闭所有窗口
        """
        # 防止重复关闭
        if hasattr(self, '_closed') and self._closed:
            print("[Image Client] 图像客户端已经关闭，跳过重复关闭")
            return

        print("[Image Client] 正在关闭图像客户端...")

        # 标记为已关闭
        self._closed = True

        # 设置运行标志为False，确保接收循环退出
        self.running = False

        # 首先关闭可能包含Qt计时器的组件
        if self._image_show:
            try:
                # 在主线程中关闭OpenCV窗口
                cv2.destroyAllWindows()  # 关闭所有OpenCV窗口
                # 等待一小段时间确保窗口关闭
                for i in range(5):
                    cv2.waitKey(1)
                print("[Image Client] OpenCV窗口已关闭")
            except Exception as e:
                print(f"[Image Client] 关闭OpenCV窗口时发生错误: {e}")

        # 关闭ZMQ通信
        try:
            if hasattr(self, '_socket') and self._socket:
                self._socket.close()  # 关闭ZMQ套接字
                print("[Image Client] ZMQ套接字已关闭")
        except Exception as e:
            print(f"[Image Client] 关闭ZMQ套接字时发生错误: {e}")

        try:
            if hasattr(self, '_context') and self._context:
                self._context.term()  # 终止ZMQ上下文
                print("[Image Client] ZMQ上下文已终止")
        except Exception as e:
            print(f"[Image Client] 终止ZMQ上下文时发生错误: {e}")

        # 关闭共享内存
        if self.tv_enable_shm:
            try:
                self.tv_image_shm.close()
                print("[Image Client] 头部相机共享内存已关闭")
            except Exception as e:
                print(f"[Image Client] 关闭头部相机共享内存时发生错误: {e}")

        if self.wrist_enable_shm:
            try:
                self.wrist_image_shm.close()
                print("[Image Client] 腕部相机共享内存已关闭")
            except Exception as e:
                print(f"[Image Client] 关闭腕部相机共享内存时发生错误: {e}")

        print("[Image Client] 图像客户端已完全关闭")

    def receive_process(self):
        """
        接收图像数据的主处理函数

        设置ZMQ连接，接收图像数据，处理并显示图像，同时进行性能评估
        """
        # 设置ZeroMQ上下文和套接字
        self._context = zmq.Context()
        self._socket = self._context.socket(zmq.SUB)  # 使用订阅者模式
        self._socket.connect(f"tcp://{self._server_address}:{self._port}")  # 连接到服务器
        self._socket.setsockopt_string(zmq.SUBSCRIBE, "")  # 订阅所有消息

        print("\nImage client has started, waiting to receive data...")
        try:
            while self.running:
                # 接收消息
                message = self._socket.recv()
                receive_time = time.time()  # 记录接收时间

                if self._enable_performance_eval:
                    # 如果启用性能评估，解析消息头部
                    header_size = struct.calcsize('dI')  # 计算头部大小（双精度浮点数+无符号整数）
                    try:
                        # 尝试提取头部和图像数据
                        header = message[:header_size]
                        jpg_bytes = message[header_size:]
                        timestamp, frame_id = struct.unpack('dI', header)  # 解包时间戳和帧ID
                    except struct.error as e:
                        print(f"[Image Client] Error unpacking header: {e}, discarding message.")
                        continue
                else:
                    # 没有头部，整个消息都是图像数据
                    jpg_bytes = message

                # 解码图像
                np_img = np.frombuffer(jpg_bytes, dtype=np.uint8)  # 将字节转换为numpy数组
                current_image = cv2.imdecode(np_img, cv2.IMREAD_COLOR)  # 解码JPEG图像
                if current_image is None:
                    print("[Image Client] Failed to decode image.")
                    continue

                # 如果启用了头部摄像头共享内存，将图像左半部分复制到共享内存
                if self.tv_enable_shm:
                    np.copyto(self.tv_img_array, np.array(current_image[:, :self.tv_img_shape[1]]))

                # 如果启用了腕部摄像头共享内存，将图像右半部分复制到共享内存
                if self.wrist_enable_shm:
                    np.copyto(self.wrist_img_array, np.array(current_image[:, -self.wrist_img_shape[1]:]))

                # 如果启用了图像显示，显示接收到的图像
                if self._image_show:
                    height, width = current_image.shape[:2]
                    resized_image = cv2.resize(current_image, (width // 2, height // 2))  # 调整图像大小以便显示
                    cv2.imshow('Image Client Stream', resized_image)
                    if cv2.waitKey(1) & 0xFF == ord('q'):  # 按'q'键退出
                        self.running = False

                # 如果启用了性能评估，更新和打印性能指标
                if self._enable_performance_eval:
                    self._update_performance_metrics(timestamp, frame_id, receive_time)
                    self._print_performance_metrics(receive_time)

        except KeyboardInterrupt:
            print("Image client interrupted by user.")
        except Exception as e:
            print(f"[Image Client] An error occurred while receiving data: {e}")
        finally:
            self._close()  # 确保在退出时关闭资源

if __name__ == "__main__":
    """
    主程序入口

    提供了两种使用示例：
    1. 使用共享内存方式接收图像
    2. 直接显示接收到的图像
    """
    # 示例1：使用共享内存方式接收图像
    # tv_img_shape = (480, 1280, 3)  # 定义图像形状
    # img_shm = shared_memory.SharedMemory(create=True, size=np.prod(tv_img_shape) * np.uint8().itemsize)  # 创建共享内存
    # img_array = np.ndarray(tv_img_shape, dtype=np.uint8, buffer=img_shm.buf)  # 创建numpy数组，使用共享内存作为缓冲区
    # img_client = ImageClient(tv_img_shape = tv_img_shape, tv_img_shm_name = img_shm.name)  # 创建客户端
    # img_client.receive_process()  # 启动接收处理

    # 示例2：直接显示接收到的图像
    # 初始化客户端，启用性能评估

    # 确保与服务器配置匹配
    # 1. 使用正确的服务器IP地址（如果在同一台机器上，使用127.0.0.1或localhost）
    # 2. 确保端口与服务器匹配（服务器默认使用5555）
    # 3. 确保Unit_Test设置与服务器匹配（服务器设置为False）

    # client = ImageClient(image_show = True, server_address='127.0.0.1', port=5555, Unit_Test=False)  # 本地测试
    client = ImageClient(image_show = True, server_address='**************', port=5555, Unit_Test=False)  # 部署测试
    client.receive_process()  # 启动接收处理