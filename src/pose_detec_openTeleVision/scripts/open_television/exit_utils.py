#!/usr/bin/env python3

"""
退出工具模块：包含与程序安全退出相关的工具函数
"""

import threading
import signal
import rospy
import time
from typing import List, Callable, Any, Optional, Dict, Union, Tuple

def setup_signal_handlers(shutdown_requested: List[bool]):
    """
    设置信号处理函数，用于捕获SIGINT和SIGTERM信号

    参数:
        shutdown_requested: 一个包含单个布尔值的列表，用作共享的关闭标志
    """
    # 定义信号处理函数
    def signal_handler(sig, frame):
        print(f"\n接收到信号 {sig}，正在关闭程序...")
        # 设置关闭标志
        shutdown_requested[0] = True

    # 注册信号处理器
    signal.signal(signal.SIGINT, signal_handler)  # Ctrl+C
    signal.signal(signal.SIGTERM, signal_handler) # 终止信号

def timeout_function(func: Callable, args: Tuple = (), kwargs: Dict = {},
                    timeout_duration: int = 5, default: Any = None) -> Any:
    """
    带超时的函数调用

    参数:
        func: 要调用的函数
        args: 函数的位置参数
        kwargs: 函数的关键字参数
        timeout_duration: 超时时间（秒）
        default: 超时时返回的默认值

    返回:
        如果函数在超时前完成，返回函数的返回值；否则返回default
    """
    class TimeoutError(Exception):
        pass

    result = [default]
    def target():
        try:
            result[0] = func(*args, **kwargs)
        except Exception as e:
            print(f"函数执行出错: {e}")

    thread = threading.Thread(target=target)
    thread.daemon = True
    thread.start()
    thread.join(timeout_duration)
    if thread.is_alive():
        print(f"函数执行超时 ({timeout_duration}秒)")
        return default
    return result[0]

def cleanup_resources(teleoperator: Any, img_shm: Any = None):
    """
    清理程序资源

    参数:
        teleoperator: TeleVisionWrapper或其模拟对象，具有end()方法
        img_shm: 共享内存对象，如果有的话
    """
    print("正在清理资源...")

    # 使用超时机制调用end()方法
    try:
        # 确保调用end()方法释放资源，设置5秒超时
        timeout_function(teleoperator.end, timeout_duration=5)
    except Exception as e:
        print(f"清理资源时发生错误: {e}")

    # 释放共享内存资源
    if img_shm is not None:
        try:
            img_shm.close()
            img_shm.unlink()  # 解除链接共享内存
            print("共享内存已释放")
        except Exception as e:
            print(f"释放共享内存时发生错误: {e}")

    # 关闭ROS节点
    try:
        rospy.signal_shutdown("程序结束")
    except Exception as e:
        print(f"关闭ROS节点时发生错误: {e}")

    print("程序已安全退出")