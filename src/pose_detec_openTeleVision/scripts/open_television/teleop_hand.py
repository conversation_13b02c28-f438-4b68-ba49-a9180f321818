from isaacgym import gymapi
from isaacgym import gymutil
from isaacgym import gymtorch

import math
import numpy as np
import torch

from TeleVision import OpenTeleVision
from Preprocessor import VuerPreprocessor
from constants_vuer import tip_indices
from dex_retargeting.retargeting_config import RetargetingConfig
from pytransform3d import rotations

from pathlib import Path
import argparse
import time
import yaml
from multiprocessing import Array, Process, shared_memory, Queue, Manager, Event, Semaphore

# 修复 trimesh.transformations.rotation_matrix 函数的问题
import trimesh.transformations
import types

# 直接修改 trimesh.transformations 模块中的 rotation_matrix 函数
def patched_rotation_matrix(angle, direction, point=None):
    """Return matrix to rotate about axis defined by point and direction.

    Parameters
    -------------
    angle     : float, or sympy.Symbol
      Angle, in radians or symbolic angle
    direction : (3,) float
      Any vector along rotation axis
    point     : (3, ) float, or None
      Origin point of rotation axis

    Returns
    -------------
    matrix : (4, 4) float, or (4, 4) sympy.Matrix
      Homogeneous transformation matrix
    """
    import numpy as np
    import math

    # 确保 direction 是一个 numpy 数组并且形状正确
    try:
        direction = np.array(direction, dtype=float)
        if direction.shape != (3,):
            if len(direction) == 3:
                direction = direction.reshape(3)
            else:
                # 如果 direction 不是一个有效的向量，使用默认值
                direction = np.array([0, 0, 1], dtype=float)
    except:
        # 如果转换失败，使用默认值
        direction = np.array([0, 0, 1], dtype=float)

    sina = math.sin(angle)
    cosa = math.cos(angle)
    direction = direction / np.linalg.norm(direction)

    # rotation matrix around unit vector
    R = np.diag([cosa, cosa, cosa])
    R += np.outer(direction, direction) * (1.0 - cosa)
    direction *= sina
    R += np.array([[0.0, -direction[2], direction[1]],
                   [direction[2], 0.0, -direction[0]],
                   [-direction[1], direction[0], 0.0]])
    M = np.identity(4)
    M[:3, :3] = R

    if point is not None:
        # rotation not around origin
        point = np.array(point[:3], dtype=float, copy=False)
        M[:3, 3] = point - np.dot(R, point)

    return M

# 替换原始函数
trimesh.transformations.rotation_matrix = patched_rotation_matrix

# 该文件实现了基于Vuer的手部远程操作系统，包括VR手部追踪和物理模拟环境

class VuerTeleop:
    """
    VuerTeleop类负责处理VR手部追踪数据并将其转换为机器人手的控制信号
    """
    def __init__(self, config_file_path):
        """
        初始化VuerTeleop对象

        参数:
            config_file_path: 包含手部重定向配置的YAML文件路径
        """
        # 设置图像分辨率
        # self.resolution = (720, 1280)
        self.resolution = (1080, 1920)
        self.crop_size_w = 0  # 图像裁剪宽度
        self.crop_size_h = 0  # 图像裁剪高度
        self.resolution_cropped = (self.resolution[0]-self.crop_size_h, self.resolution[1]-2*self.crop_size_w)  # 裁剪后的分辨率

        # 计算最终图像形状（左右相机图像拼接）
        self.img_shape = (self.resolution_cropped[0], 2 * self.resolution_cropped[1], 3)
        self.img_height, self.img_width = self.resolution_cropped[:2]

        # 创建共享内存用于图像传输
        self.shm = shared_memory.SharedMemory(create=True, size=np.prod(self.img_shape) * np.uint8().itemsize)
        self.img_array = np.ndarray((self.img_shape[0], self.img_shape[1], 3), dtype=np.uint8, buffer=self.shm.buf)

        # 初始化OpenTeleVision和VuerPreprocessor
        self.tv = OpenTeleVision(False, self.resolution_cropped, self.shm.name, "./pem/cert.pem", "./pem/key.pem")
        self.processor = VuerPreprocessor()

        # 加载手部重定向配置
        RetargetingConfig.set_default_urdf_dir('.')
        with Path(config_file_path).open('r') as f:
            cfg = yaml.safe_load(f)
        left_retargeting_config = RetargetingConfig.from_dict(cfg['left'])
        right_retargeting_config = RetargetingConfig.from_dict(cfg['right'])
        self.left_retargeting = left_retargeting_config.build()
        self.right_retargeting = right_retargeting_config.build()

    def step(self):
        """
        处理一帧VR追踪数据并计算手部控制信号

        返回:
            head_rmat: 头部旋转矩阵
            left_pose: 左手位姿（位置和四元数）
            right_pose: 右手位姿（位置和四元数）
            left_qpos: 左手关节角度
            right_qpos: 右手关节角度
        """
        # 从处理器获取头部和手部的变换矩阵
        head_mat, left_wrist_mat, right_wrist_mat, left_hand_mat, right_hand_mat = self.processor.process(self.tv)

        # 提取头部旋转矩阵
        head_rmat = head_mat[:3, :3]

        # 计算左手位姿（位置和四元数）
        left_pose = np.concatenate([left_wrist_mat[:3, 3] + np.array([-0.6, 0, 1.6]),  # 位置偏移
                                    rotations.quaternion_from_matrix(left_wrist_mat[:3, :3])[[1, 2, 3, 0]]])  # 旋转四元数

        # 计算右手位姿（位置和四元数）
        right_pose = np.concatenate([right_wrist_mat[:3, 3] + np.array([-0.6, 0, 1.6]),  # 位置偏移
                                     rotations.quaternion_from_matrix(right_wrist_mat[:3, :3])[[1, 2, 3, 0]]])  # 旋转四元数

        # 使用重定向模型计算左手关节角度并重新排序
        left_qpos = self.left_retargeting.retarget(left_hand_mat[tip_indices])[[4, 5, 6, 7, 10, 11, 8, 9, 0, 1, 2, 3]]

        # 使用重定向模型计算右手关节角度并重新排序
        right_qpos = self.right_retargeting.retarget(right_hand_mat[tip_indices])[[4, 5, 6, 7, 10, 11, 8, 9, 0, 1, 2, 3]]

        return head_rmat, left_pose, right_pose, left_qpos, right_qpos

class Sim:
    """
    Sim类负责创建和管理物理模拟环境，包括机器人手、桌子、立方体等物体
    """
    def __init__(self,
                 print_freq=False):
        """
        初始化模拟环境

        参数:
            print_freq: 是否打印模拟频率
        """
        self.print_freq = print_freq

        # 初始化Isaac Gym
        self.gym = gymapi.acquire_gym()

        # 配置模拟参数
        sim_params = gymapi.SimParams()
        sim_params.dt = 1 / 60  # 模拟时间步长
        sim_params.substeps = 2  # 每个时间步的子步数
        sim_params.up_axis = gymapi.UP_AXIS_Z  # 设置Z轴为上方向
        sim_params.gravity = gymapi.Vec3(0.0, 0.0, -9.81)  # 重力设置
        # PhysX物理引擎参数
        sim_params.physx.solver_type = 1
        sim_params.physx.num_position_iterations = 4
        sim_params.physx.num_velocity_iterations = 1
        sim_params.physx.max_gpu_contact_pairs = 8388608
        sim_params.physx.contact_offset = 0.002
        sim_params.physx.friction_offset_threshold = 0.001
        sim_params.physx.friction_correlation_distance = 0.0005
        sim_params.physx.rest_offset = 0.0
        sim_params.physx.use_gpu = True  # 使用GPU加速
        sim_params.use_gpu_pipeline = False

        # 创建模拟实例
        self.sim = self.gym.create_sim(0, 0, gymapi.SIM_PHYSX, sim_params)
        if self.sim is None:
            print("*** Failed to create sim")
            quit()

        # 添加地面
        plane_params = gymapi.PlaneParams()
        plane_params.distance = 0.0
        plane_params.normal = gymapi.Vec3(0.0, 0.0, 1.0)
        self.gym.add_ground(self.sim, plane_params)

        # 加载桌子资产
        table_asset_options = gymapi.AssetOptions()
        table_asset_options.disable_gravity = True
        table_asset_options.fix_base_link = True
        table_asset = self.gym.create_box(self.sim, 0.8, 0.8, 0.1, table_asset_options)

        # 加载立方体资产
        cube_asset_options = gymapi.AssetOptions()
        cube_asset_options.density = 10
        cube_asset = self.gym.create_box(self.sim, 0.05, 0.05, 0.05, cube_asset_options)

        # 加载左右手资产
        asset_root = "./"
        left_asset_path = "inspire_hand/inspire_hand_left.urdf"
        right_asset_path = "inspire_hand/inspire_hand_right.urdf"
        asset_options = gymapi.AssetOptions()
        asset_options.fix_base_link = True
        asset_options.default_dof_drive_mode = gymapi.DOF_MODE_POS
        left_asset = self.gym.load_asset(self.sim, asset_root, left_asset_path, asset_options)
        right_asset = self.gym.load_asset(self.sim, asset_root, right_asset_path, asset_options)
        self.dof = self.gym.get_asset_dof_count(left_asset)  # 获取自由度数量

        # 设置环境网格
        num_envs = 1
        num_per_row = int(math.sqrt(num_envs))
        env_spacing = 1.25
        env_lower = gymapi.Vec3(-env_spacing, 0.0, -env_spacing)
        env_upper = gymapi.Vec3(env_spacing, env_spacing, env_spacing)
        np.random.seed(0)
        self.env = self.gym.create_env(self.sim, env_lower, env_upper, num_per_row)

        # 创建桌子
        pose = gymapi.Transform()
        pose.p = gymapi.Vec3(0, 0, 1.2)  # 位置
        pose.r = gymapi.Quat(0, 0, 0, 1)  # 旋转
        table_handle = self.gym.create_actor(self.env, table_asset, pose, 'table', 0)
        color = gymapi.Vec3(0.5, 0.5, 0.5)  # 灰色
        self.gym.set_rigid_body_color(self.env, table_handle, 0, gymapi.MESH_VISUAL_AND_COLLISION, color)

        # 创建立方体
        pose = gymapi.Transform()
        pose.p = gymapi.Vec3(0, 0, 1.25)  # 位置（在桌子上方）
        pose.r = gymapi.Quat(0, 0, 0, 1)  # 旋转
        cube_handle = self.gym.create_actor(self.env, cube_asset, pose, 'cube', 0)
        color = gymapi.Vec3(1, 0.5, 0.5)  # 粉红色
        self.gym.set_rigid_body_color(self.env, cube_handle, 0, gymapi.MESH_VISUAL_AND_COLLISION, color)

        # 创建左手
        pose = gymapi.Transform()
        pose.p = gymapi.Vec3(-0.6, 0, 1.6)  # 初始位置
        pose.r = gymapi.Quat(0, 0, 0, 1)  # 初始旋转
        self.left_handle = self.gym.create_actor(self.env, left_asset, pose, 'left', 1, 1)
        self.gym.set_actor_dof_states(self.env, self.left_handle, np.zeros(self.dof, gymapi.DofState.dtype),
                                      gymapi.STATE_ALL)
        left_idx = self.gym.get_actor_index(self.env, self.left_handle, gymapi.DOMAIN_SIM)

        # 创建右手
        pose = gymapi.Transform()
        pose.p = gymapi.Vec3(-0.6, 0, 1.6)  # 初始位置
        pose.r = gymapi.Quat(0, 0, 0, 1)  # 初始旋转
        self.right_handle = self.gym.create_actor(self.env, right_asset, pose, 'right', 1, 1)
        self.gym.set_actor_dof_states(self.env, self.right_handle, np.zeros(self.dof, gymapi.DofState.dtype),
                                      gymapi.STATE_ALL)
        right_idx = self.gym.get_actor_index(self.env, self.right_handle, gymapi.DOMAIN_SIM)

        # 获取根状态张量用于控制手的位置和旋转
        self.root_state_tensor = self.gym.acquire_actor_root_state_tensor(self.sim)
        self.gym.refresh_actor_root_state_tensor(self.sim)
        self.root_states = gymtorch.wrap_tensor(self.root_state_tensor)
        self.left_root_states = self.root_states[left_idx]
        self.right_root_states = self.root_states[right_idx]

        # 创建默认观察视角
        self.viewer = self.gym.create_viewer(self.sim, gymapi.CameraProperties())
        if self.viewer is None:
            print("*** Failed to create viewer")
            quit()
        cam_pos = gymapi.Vec3(1, 1, 2)
        cam_target = gymapi.Vec3(0, 0, 1)
        self.gym.viewer_camera_look_at(self.viewer, None, cam_pos, cam_target)

        # 设置相机偏移量
        self.cam_lookat_offset = np.array([1, 0, 0])  # 相机观察方向偏移
        self.left_cam_offset = np.array([0, 0.033, 0])  # 左眼相机偏移
        self.right_cam_offset = np.array([0, -0.033, 0])  # 右眼相机偏移
        self.cam_pos = np.array([-0.6, 0, 1.6])  # 相机基础位置

        # 创建左眼第一人称相机
        camera_props = gymapi.CameraProperties()
        # camera_props.width = 1280
        # camera_props.height = 720
        camera_props.width = 1920
        camera_props.height = 1080
        self.left_camera_handle = self.gym.create_camera_sensor(self.env, camera_props)
        self.gym.set_camera_location(self.left_camera_handle,
                                     self.env,
                                     gymapi.Vec3(*(self.cam_pos + self.left_cam_offset)),
                                     gymapi.Vec3(*(self.cam_pos + self.left_cam_offset + self.cam_lookat_offset)))

        # 创建右眼第一人称相机
        camera_props = gymapi.CameraProperties()
        # camera_props.width = 1280
        # camera_props.height = 720
        camera_props.width = 1920
        camera_props.height = 1080
        self.right_camera_handle = self.gym.create_camera_sensor(self.env, camera_props)
        self.gym.set_camera_location(self.right_camera_handle,
                                     self.env,
                                     gymapi.Vec3(*(self.cam_pos + self.right_cam_offset)),
                                     gymapi.Vec3(*(self.cam_pos + self.right_cam_offset + self.cam_lookat_offset)))

    def step(self, head_rmat, left_pose, right_pose, left_qpos, right_qpos):
        """
        执行一步模拟，更新手部位置和姿态，并渲染场景

        参数:
            head_rmat: 头部旋转矩阵，用于控制相机视角
            left_pose: 左手位姿（位置和四元数）
            right_pose: 右手位姿（位置和四元数）
            left_qpos: 左手关节角度
            right_qpos: 右手关节角度

        返回:
            left_image: 左眼相机图像
            right_image: 右眼相机图像
        """
        # 如果需要，记录开始时间以计算频率
        if self.print_freq:
            start = time.time()

        # 更新左右手的位置和旋转
        self.left_root_states[0:7] = torch.tensor(left_pose, dtype=float)
        self.right_root_states[0:7] = torch.tensor(right_pose, dtype=float)
        self.gym.set_actor_root_state_tensor(self.sim, gymtorch.unwrap_tensor(self.root_states))

        # 设置左手关节角度
        left_states = np.zeros(self.dof, dtype=gymapi.DofState.dtype)
        left_states['pos'] = left_qpos
        self.gym.set_actor_dof_states(self.env, self.left_handle, left_states, gymapi.STATE_POS)

        # 设置右手关节角度
        right_states = np.zeros(self.dof, dtype=gymapi.DofState.dtype)
        right_states['pos'] = right_qpos
        self.gym.set_actor_dof_states(self.env, self.right_handle, right_states, gymapi.STATE_POS)

        # 执行物理模拟
        self.gym.simulate(self.sim)
        self.gym.fetch_results(self.sim, True)
        self.gym.step_graphics(self.sim)
        self.gym.render_all_camera_sensors(self.sim)
        self.gym.refresh_actor_root_state_tensor(self.sim)

        # 根据头部旋转更新相机偏移
        curr_lookat_offset = self.cam_lookat_offset @ head_rmat.T  # 计算当前观察方向偏移
        curr_left_offset = self.left_cam_offset @ head_rmat.T  # 计算当前左眼偏移
        curr_right_offset = self.right_cam_offset @ head_rmat.T  # 计算当前右眼偏移

        # 更新左眼相机位置和方向
        self.gym.set_camera_location(self.left_camera_handle,
                                     self.env,
                                     gymapi.Vec3(*(self.cam_pos + curr_left_offset)),
                                     gymapi.Vec3(*(self.cam_pos + curr_left_offset + curr_lookat_offset)))

        # 更新右眼相机位置和方向
        self.gym.set_camera_location(self.right_camera_handle,
                                     self.env,
                                     gymapi.Vec3(*(self.cam_pos + curr_right_offset)),
                                     gymapi.Vec3(*(self.cam_pos + curr_right_offset + curr_lookat_offset)))

        # 获取左右眼相机图像
        left_image = self.gym.get_camera_image(self.sim, self.env, self.left_camera_handle, gymapi.IMAGE_COLOR)
        right_image = self.gym.get_camera_image(self.sim, self.env, self.right_camera_handle, gymapi.IMAGE_COLOR)

        # 处理图像格式（去除alpha通道）
        left_image = left_image.reshape(left_image.shape[0], -1, 4)[..., :3]
        right_image = right_image.reshape(right_image.shape[0], -1, 4)[..., :3]

        # 更新观察者视图
        self.gym.draw_viewer(self.viewer, self.sim, True)
        self.gym.sync_frame_time(self.sim)

        # 如果需要，计算并打印模拟频率
        if self.print_freq:
            end = time.time()
            print('Frequency:', 1 / (end - start))

        return left_image, right_image

    def end(self):
        """
        清理模拟环境资源
        """
        self.gym.destroy_viewer(self.viewer)  # 销毁观察者
        self.gym.destroy_sim(self.sim)  # 销毁模拟环境


if __name__ == '__main__':
    """
    主程序入口：创建远程操作和模拟环境，并运行主循环
    """
    # 初始化远程操作器和模拟器
    teleoperator = VuerTeleop('./configs/inspire_hand.yml')
    simulator = Sim()

    try:
        # 主循环
        while True:
            # 获取手部追踪数据
            head_rmat, left_pose, right_pose, left_qpos, right_qpos = teleoperator.step()
            print("left_pose: ")
            print(left_pose)
            print("right_pose: ")
            print(right_pose)
            # 更新模拟环境并获取相机图像
            left_img, right_img = simulator.step(head_rmat, left_pose, right_pose, left_qpos, right_qpos)
            # 将左右眼图像拼接并复制到共享内存
            np.copyto(teleoperator.img_array, np.hstack((left_img, right_img)))
    except KeyboardInterrupt:
        # 处理键盘中断
        simulator.end()
        exit(0)
