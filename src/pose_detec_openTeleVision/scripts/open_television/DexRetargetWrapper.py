import os
import sys
# 设置导入路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(current_dir)

from constants_vuer import tip_indices
from dex_retargeting.retargeting_config import RetargetingConfig

import yaml
from pathlib import Path

# 修复 trimesh.transformations.rotation_matrix 函数的问题
import trimesh.transformations
import types
from motion_utils import patched_rotation_matrix
# 替换原始函数
trimesh.transformations.rotation_matrix = patched_rotation_matrix

class DexRetargetWrapper:
    def __init__(self, config_file_path):
        # 加载手部重定向配置
        RetargetingConfig.set_default_urdf_dir('.')
        with Path(config_file_path).open('r') as f:
            cfg = yaml.safe_load(f)
        left_retargeting_config = RetargetingConfig.from_dict(cfg['left'])
        right_retargeting_config = RetargetingConfig.from_dict(cfg['right'])    
        self.left_retargeting = left_retargeting_config.build()
        self.right_retargeting = right_retargeting_config.build()

        self.left_retargeting_joint_names = self.left_retargeting.joint_names
        self.right_retargeting_joint_names = self.right_retargeting.joint_names

        self.left_inspire_api_joint_names  = [ 'L_pinky_proximal_joint', 'L_ring_proximal_joint', 'L_middle_proximal_joint',
                                               'L_index_proximal_joint', 'L_thumb_proximal_pitch_joint', 'L_thumb_proximal_yaw_joint' ]
        self.right_inspire_api_joint_names = [ 'R_pinky_proximal_joint', 'R_ring_proximal_joint', 'R_middle_proximal_joint',
                                               'R_index_proximal_joint', 'R_thumb_proximal_pitch_joint', 'R_thumb_proximal_yaw_joint' ]

        self.left_dex_retargeting_to_hardware = [ self.left_retargeting_joint_names.index(name) for name in self.left_inspire_api_joint_names]
        self.right_dex_retargeting_to_hardware = [ self.right_retargeting_joint_names.index(name) for name in self.right_inspire_api_joint_names]

    def retarget(self, left_hand_mat, right_hand_mat):
        # 使用重定向模型计算左手关节角度并重新排序
        left_qpos = self.left_retargeting.retarget(left_hand_mat[tip_indices])[self.left_dex_retargeting_to_hardware]

        # 使用重定向模型计算右手关节角度并重新排序
        right_qpos = self.right_retargeting.retarget(right_hand_mat[tip_indices])[self.right_dex_retargeting_to_hardware]

        return left_qpos, right_qpos
