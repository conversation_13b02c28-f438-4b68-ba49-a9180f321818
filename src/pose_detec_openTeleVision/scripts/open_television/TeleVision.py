import time
from vuer import Vuer
from vuer.events import ClientEvent
from vuer.schemas import ImageBackground, group, Hands, WebRTCStereoVideoPlane, DefaultScene
from multiprocessing import Array, Process, shared_memory, Queue, Manager, Event, Semaphore
import numpy as np
import asyncio
import cv2

class OpenTeleVision:
    def __init__(self, binocular, img_shape, shm_name, cert_file="./cert.pem", key_file="./key.pem", ngrok=False):
        self.binocular = binocular
        # self.img_shape = (img_shape[0], 2*img_shape[1], 3)
        self.img_height = img_shape[0]
        # 如果是双目模式，宽度需要减半，因为图像被分为左右两部分
        if binocular:
            self.img_width = img_shape[1] // 2
        else:
            self.img_width = img_shape[1]

        if ngrok:
            self.app = Vuer(host='0.0.0.0', queries=dict(grid=False), queue_len=3)
        else:
            self.app = Vuer(host='0.0.0.0', cert=cert_file, key=key_file, queries=dict(grid=False), queue_len=3)

        self.app.add_handler("HAND_MOVE")(self.on_hand_move)
        self.app.add_handler("CAMERA_MOVE")(self.on_cam_move)

        existing_shm = shared_memory.SharedMemory(name=shm_name)
        self.img_array = np.ndarray(img_shape, dtype=np.uint8, buffer=existing_shm.buf)

        if binocular:
            self.app.spawn(start=False)(self.main_image_binocular)
        else:
            self.app.spawn(start=False)(self.main_image_monocular)

        # 创建共享数组用于存储手部和头部跟踪数据
        # 每个手部矩阵为4x4，存储为16个双精度浮点数
        self.left_hand_shared = Array('d', 16, lock=True)
        self.right_hand_shared = Array('d', 16, lock=True)
        # 每只手有25个关键点，每个关键点有3个坐标 (x,y,z)
        self.left_landmarks_shared = Array('d', 75, lock=True)
        self.right_landmarks_shared = Array('d', 75, lock=True)
        # 头部位置矩阵 (4x4)
        self.head_matrix_shared = Array('d', 16, lock=True)

        # 在单独的进程中运行Vuer
        self.process = Process(target=self.run)
        self.process.daemon = True
        self.process.start()


    def run(self):
        """
        在单独的进程中运行Vuer服务器
        """
        self.app.run()

    async def on_cam_move(self, event, session, fps=60):
        """
        处理相机移动事件

        当用户移动视角时更新头部矩阵和宽高比

        参数:
            event: 包含相机数据的事件对象
            session: Vuer会话
            fps (int): 帧率
        """
        try:
            # 更新头部位置矩阵
            self.head_matrix_shared[:] = event.value["camera"]["matrix"]
            # 更新视图宽高比
            self.aspect_shared.value = event.value['camera']['aspect']
        except:
            pass  # 忽略可能的错误，确保程序继续运行

    async def on_hand_move(self, event, session, fps=60):
        """
        处理手部移动事件

        当用户移动手部时更新手部位置和关键点数据

        参数:
            event: 包含手部数据的事件对象
            session: Vuer会话
            fps (int): 帧率
        """
        try:
            # 更新左右手的位置矩阵
            self.left_hand_shared[:] = event.value["leftHand"]
            self.right_hand_shared[:] = event.value["rightHand"]
            # 更新左右手的关键点数据
            self.left_landmarks_shared[:] = np.array(event.value["leftLandmarks"]).flatten()
            self.right_landmarks_shared[:] = np.array(event.value["rightLandmarks"]).flatten()
        except:
            pass  # 忽略可能的错误，确保程序继续运行

    async def main_image_binocular(self, session, fps=60):
        """
        双目模式下的主图像处理函数

        将图像分为左右两部分，分别发送到左右眼

        参数:
            session: Vuer会话
            fps (int): 帧率
        """
        session.upsert @ Hands(fps=fps, stream=True, key="hands", showLeft=False, showRight=False)
        while True:
            display_image = cv2.cvtColor(self.img_array, cv2.COLOR_BGR2RGB)
            # aspect_ratio = self.img_width / self.img_height
            session.upsert(
                [
                    ImageBackground(
                        display_image[:, :self.img_width],
                        aspect=1.778,
                        height=1,
                        distanceToCamera=1,
                        # The underlying rendering engine supported a layer binary bitmask for both objects and the camera.
                        # Below we set the two image planes, left and right, to layers=1 and layers=2.
                        # Note that these two masks are associated with left eye’s camera and the right eye’s camera.
                        layers=1,
                        format="jpeg",
                        quality=50,
                        key="background-left",
                        interpolate=True,
                    ),
                    ImageBackground(
                        display_image[:, self.img_width:],
                        aspect=1.778,
                        height=1,
                        distanceToCamera=1,
                        layers=2,
                        format="jpeg",
                        quality=50,
                        key="background-right",
                        interpolate=True,
                    ),
                ],
                to="bgChildren",
            )
            # 'jpeg' encoding should give you about 30fps with a 16ms wait in-between.
            await asyncio.sleep(0.016 * 2)

    async def main_image_monocular(self, session, fps=60):
        """
        单目模式下的主图像处理函数

        发送整个图像作为单一视图

        参数:
            session: Vuer会话
            fps (int): 帧率
        """
        session.upsert @ Hands(fps=fps, stream=True, key="hands", showLeft=False, showRight=False)
        while True:
            display_image = cv2.cvtColor(self.img_array, cv2.COLOR_BGR2RGB)
            # aspect_ratio = self.img_width / self.img_height
            session.upsert(
                [
                    ImageBackground(
                        display_image,
                        aspect=1.778,
                        height=1,
                        distanceToCamera=1.5,
                        format="jpeg",
                        quality=20,
                        key="background-mono",
                        interpolate=True,
                    ),
                ],
                to="bgChildren",
            )
            await asyncio.sleep(0.016)

    @property
    def left_hand(self):
        """
        获取左手位置矩阵

        返回:
            numpy.ndarray: 4x4的变换矩阵，表示左手的位置和方向
        """
        return np.array(self.left_hand_shared[:]).reshape(4, 4, order="F")


    @property
    def right_hand(self):
        """
        获取右手位置矩阵

        返回:
            numpy.ndarray: 4x4的变换矩阵，表示右手的位置和方向
        """
        return np.array(self.right_hand_shared[:]).reshape(4, 4, order="F")


    @property
    def left_landmarks(self):
        """
        获取左手关键点数据

        返回:
            numpy.ndarray: 形状为(25, 3)的数组，包含25个关键点的3D坐标
        """
        return np.array(self.left_landmarks_shared[:]).reshape(25, 3)

    @property
    def right_landmarks(self):
        """
        获取右手关键点数据

        返回:
            numpy.ndarray: 形状为(25, 3)的数组，包含25个关键点的3D坐标
        """
        return np.array(self.right_landmarks_shared[:]).reshape(25, 3)

    @property
    def head_matrix(self):
        """
        获取头部位置矩阵

        返回:
            numpy.ndarray: 4x4的变换矩阵，表示头部的位置和方向
        """
        return np.array(self.head_matrix_shared[:]).reshape(4, 4, order="F")

    @property
    def aspect(self):
        """
        获取当前视图的宽高比

        返回:
            float: 视图宽高比
        """
        return float(self.aspect_shared.value)

    def close(self):
        """
        关闭OpenTeleVision并释放资源

        终止Vuer进程并清理共享内存
        """
        # 防止重复关闭
        if hasattr(self, '_closed') and self._closed:
            print("OpenTeleVision已经关闭，跳过重复关闭")
            return

        print("正在关闭OpenTeleVision...")

        # 标记为已关闭
        self._closed = True

        # 终止Vuer进程
        if hasattr(self, 'process') and self.process and self.process.is_alive():
            try:
                self.process.terminate()
                self.process.join(timeout=2.0)  # 等待最多2秒
                if self.process.is_alive():
                    self.process.kill()  # 如果进程仍然存活，强制终止
                print("Vuer进程已终止")
            except Exception as e:
                print(f"终止Vuer进程时发生错误: {e}")

        print("OpenTeleVision已关闭")


if __name__ == '__main__':
    """
    主程序入口 - 用于测试TeleVision类

    创建共享内存、启动图像接收线程，并初始化TeleVision实例
    """
    import os
    import sys
    # 设置导入路径
    current_dir = os.path.dirname(os.path.abspath(__file__))
    parent_dir = os.path.dirname(current_dir)
    sys.path.append(parent_dir)
    import threading
    from image_server.image_client import ImageClient

    # 创建图像相关资源
    img_shape = (480, 640, 3)  # 图像形状：高度480，宽度1280 (640*2)，3个颜色通道
    # 创建共享内存用于存储图像数据
    img_shm = shared_memory.SharedMemory(create=True, size=np.prod(img_shape) * np.uint8().itemsize)
    img_array = np.ndarray(img_shape, dtype=np.uint8, buffer=img_shm.buf)
    # 创建图像客户端
    img_client = ImageClient(tv_img_shape=img_shape, tv_img_shm_name=img_shm.name, image_show=True, port=5555)
    # 在后台线程中接收图像
    image_receive_thread = threading.Thread(target=img_client.receive_process, daemon=True)
    image_receive_thread.start()

    # 创建TeleVision实例（双目模式）

    tv = OpenTeleVision(False, img_shape, img_shm.name, "./pem/cert.pem", "./pem/key.pem")
    print("vuer unit test program running...")
    print("you can press ^C to interrupt program.")
    # 主循环，保持程序运行
    while True:
        time.sleep(0.03)  # 约33Hz的更新频率
