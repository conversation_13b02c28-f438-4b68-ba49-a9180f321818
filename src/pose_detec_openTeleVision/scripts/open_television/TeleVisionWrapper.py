import os
import sys
# 设置导入路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(current_dir)

import numpy as np
from TeleVision import OpenTeleVision
from constants_vuer import *
from motion_utils import mat_update, fast_mat_inv

from image_server.image_server import ImageServer
from image_server.image_client import ImageClient
import threading
import cv2

"""
坐标系基础定义：
    (基础)OpenXR 坐标系: Y 轴向上, Z 轴向后, X 轴向右
    (基础)Robot 坐标系: Z 轴向上, Y 轴向左, X 轴向前
    备注: Vuer 的所有原始数据都遵循 OpenXR 坐标系 (WORLD 世界坐标系)

在 Robot 坐标系基础下，手腕的初始姿态定义:
    (左手腕) XR/Apple Vision Pro 坐标系定义:
    X 轴从手腕指向中指方向
    Y 轴从食指指向小指方向
    Z 轴从手掌指向手背方向

    (右手腕) XR/Apple Vision Pro 坐标系定义:
    X 轴从手腕指向中指方向
    Y 轴从小指指向食指方向
    Z 轴从手掌指向手背方向

    (左手腕) 启江3代臂末端坐标系定义:
    X 轴从手腕指向中指方向
    Y 轴从手背指向手掌方向
    Z 轴从食指指向小指方向

    (右手腕) 启江3代臂末端坐标系定义:
    X 轴从手腕指向中指方向
    Y 轴从手背指向手掌方向
    Z 轴从小指指向食指方向

apv->qj3:
    左手腕: 绕x轴旋转-90度
    右手腕: 绕x轴旋转-90度
"""

class TeleVisionWrapper:
    def __init__(self, binocular, img_shape, img_shm_name, cert_path, key_path):
        # self.enable_head_camera_view(img_shape, img_shm_name)
        self.tv = OpenTeleVision(binocular, img_shape, img_shm_name, cert_file=cert_path, key_file=key_path)

    def enable_head_camera_view(self, img_shape, img_shm_name):
        # 相机服务端启动
        config = {
            'fps': 30,
            'head_camera_type': 'realsense',
            'head_camera_image_shape': img_shape,  # 提高分辨率到HD (720p)
            'head_camera_id_numbers': ["143322072142"]
        }
        self.img_server = ImageServer(config, Unit_Test=False)
        self.image_server_thread = threading.Thread(target=self.img_server.send_process, daemon=True)
        self.image_server_thread.start()

        # 创建图像客户端
        self.img_client = ImageClient(tv_img_shape=img_shape, tv_img_shm_name=img_shm_name, image_show=False, port=5555)
        # 在后台线程中接收图像
        self.image_client_thread = threading.Thread(target=self.img_client.receive_process, daemon=True)
        self.image_client_thread.start()

    def get_data(self):

        # --------------------------------wrist-------------------------------------

        # 从 XR（TeleVision）中获取的姿态矩阵是以 OpenXR Convention 为基础的基坐标系矩阵
        head_vuer_mat, head_flag = mat_update(const_head_vuer_mat, self.tv.head_matrix.copy())
        left_wrist_vuer_mat, left_wrist_flag  = mat_update(const_left_wrist_vuer_mat, self.tv.left_hand.copy())
        right_wrist_vuer_mat, right_wrist_flag = mat_update(const_right_wrist_vuer_mat, self.tv.right_hand.copy())

        # 通过相似变换, 将一个在OpenXR坐标系下定义的姿态矩阵 (VuerMat) 转换为在Robot Convention (URDF 坐标系) 下的等价姿态表示
        head_mat = T_robot_openxr @ head_vuer_mat @ fast_mat_inv(T_robot_openxr)
        left_wrist_mat  = T_robot_openxr @ left_wrist_vuer_mat @ fast_mat_inv(T_robot_openxr)
        right_wrist_mat = T_robot_openxr @ right_wrist_vuer_mat @ fast_mat_inv(T_robot_openxr)

        # 将左右手腕的姿态矩阵从 XR/Apple Vision Pro 的手腕坐标系定义（OpenXR Convention）转换为 启江3代机械臂 URDF 中定义的手腕坐标系
        armgen3_left_wrist = left_wrist_mat @ (T_to_urdf_wrist if left_wrist_flag else np.eye(4))
        armgen3_right_wrist = right_wrist_mat @ (T_to_urdf_wrist if right_wrist_flag else np.eye(4))

        # 将左右手腕的位置信息从 世界坐标系（WORLD） 转换为 头部坐标系（HEAD），仅进行平移（Translation）转换，不涉及旋转（Rotation）。
        armgen3_left_wrist[0:3, 3]  = armgen3_left_wrist[0:3, 3] - head_mat[0:3, 3]
        armgen3_right_wrist[0:3, 3] = armgen3_right_wrist[0:3, 3] - head_mat[0:3, 3]

        # --------------------------------hand-------------------------------------

        # 关键点坐标扩展成齐次形式
        # 将 (25, 3) 的点转换为 (4, 25) 的齐次向量矩阵，方便做后续仿射变换（4x4矩阵直接乘）
        # Now under (basis) OpenXR Convention, mat shape like this:
        #    x0 x1 x2 ··· x23 x24
        #    y0 y1 y1 ··· y23 y24
        #    z0 z1 z2 ··· z23 z24
        #     1  1  1 ···   1   1
        left_hand_vuer_mat  = np.concatenate([self.tv.left_landmarks.copy().T, np.ones((1, self.tv.left_landmarks.shape[0]))])
        right_hand_vuer_mat = np.concatenate([self.tv.right_landmarks.copy().T, np.ones((1, self.tv.right_landmarks.shape[0]))])

        # 从 OpenXR Convention 到 Robot Convention 的基变换
        # 不涉及相对于世界原点的位置变化（不做仿射变换中的平移），仅仅是方向基变换。
        left_hand_mat  = T_robot_openxr @ left_hand_vuer_mat
        right_hand_mat = T_robot_openxr @ right_hand_vuer_mat

        # 将关键点转换为以手腕为原点的手部坐标
        left_hand_mat_wb  = fast_mat_inv(left_wrist_mat) @ left_hand_mat
        right_hand_mat_wb = fast_mat_inv(right_wrist_mat) @ right_hand_mat
        # Change hand convention: HandMat ((Left Hand) XR/AppleVisionPro Convention) to UnitreeHandMat((Left Hand URDF) Unitree Convention)
        # Reason for left multiply : T_to_unitree_hand @ left_hand_mat_wb ==> (4,4) @ (4,25) ==> (4,25), (4,25)[0:3, :] ==> (3,25), (3,25).T ==> (25,3)           
        # Now under (Left Hand URDF) Unitree Convention, mat shape like this:
        #    [x0, y0, z0]
        #    [x1, y1, z1]
        #    ···
        #    [x23,y23,z23] 
        #    [x24,y24,z24]               
        inspire_left_hand  = (T_to_inspire_hand @ left_hand_mat_wb)[0:3, :].T
        inspire_right_hand = (T_to_inspire_hand @ right_hand_mat_wb)[0:3, :].T

        # --------------------------------offset-------------------------------------

        head_rmat = head_mat[:3, :3]
        # The origin of the coordinate for IK Solve is the WAIST joint motor. You can use teleop/robot_control/robot_arm_ik.py Unit_Test to check it.
        # The origin of the coordinate of unitree_left_wrist is HEAD. So it is necessary to translate the origin of unitree_left_wrist from HEAD to WAIST.
        armgen3_left_wrist[0, 3] +=0.2
        armgen3_right_wrist[0,3] +=0.2
        armgen3_left_wrist[2, 3] +=1.8
        armgen3_right_wrist[2,3] +=1.8

        return head_rmat, armgen3_left_wrist, armgen3_right_wrist, inspire_left_hand, inspire_right_hand
    
    def end(self):
        """
        清理资源
        """
        # 防止重复关闭
        if hasattr(self, '_closed') and self._closed:
            print("VuerTeleop已经关闭，跳过重复关闭")
            return

        print("正在关闭图像服务器和客户端...")
        # 标记为已关闭
        self._closed = True

        # 首先关闭可能包含Qt计时器的组件
        if hasattr(self, 'img_client') and hasattr(self.img_client, '_image_show') and self.img_client._image_show:
            try:
                # 在主线程中关闭OpenCV窗口
                cv2.destroyAllWindows()
                # 等待一小段时间确保窗口关闭
                for i in range(5):
                    cv2.waitKey(1)
                print("已关闭所有OpenCV窗口")
            except Exception as e:
                print(f"关闭OpenCV窗口时出错: {e}")

        # 关闭OpenTeleVision
        try:
            if hasattr(self, 'tv'):
                self.tv.close()
                print("OpenTeleVision已关闭")
        except Exception as e:
            print(f"关闭OpenTeleVision时出错: {e}")

        # 关闭图像客户端
        try:
            if hasattr(self, 'img_client'):
                self.img_client._close()
                print("图像客户端已关闭")
        except Exception as e:
            print(f"关闭图像客户端时出错: {e}")

        # 关闭图像服务器
        try:
            if hasattr(self, 'img_server'):
                self.img_server._close()
                print("图像服务器已关闭")
        except Exception as e:
            print(f"关闭图像服务器时出错: {e}")

        # 等待线程结束
        if hasattr(self, 'image_server_thread') and self.image_server_thread.is_alive():
            try:
                self.image_server_thread.join(timeout=2.0)  # 等待最多2秒
                print("图像服务器线程已结束")
            except Exception as e:
                print(f"等待图像服务器线程结束时出错: {e}")

        if hasattr(self, 'image_client_thread') and self.image_client_thread.is_alive():
            try:
                self.image_client_thread.join(timeout=2.0)  # 等待最多2秒
                print("图像客户端线程已结束")
            except Exception as e:
                print(f"等待图像客户端线程结束时出错: {e}")

        # 释放共享内存
        if hasattr(self, 'img_shm'):
            try:
                self.img_shm.close()
                self.img_shm.unlink()  # 删除共享内存
                print("共享内存已释放")
            except Exception as e:
                print(f"释放共享内存时出错: {e}")

        print("资源清理完成")