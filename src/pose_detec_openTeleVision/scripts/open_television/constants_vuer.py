import numpy as np

tip_indices = [4, 9, 14, 19, 24]

hand2inspire = np.array([[0, -1, 0, 0],
                         [0, 0, -1, 0],
                         [1, 0, 0, 0],
                         [0, 0, 0, 1]])


grd_yup2grd_zup = np.array([[0, 0, -1, 0],
                            [-1, 0, 0, 0],
                            [0, 1, 0, 0],
                            [0, 0, 0, 1]])

const_head_vuer_mat = np.array([[1, 0, 0, 0],
                                [0, 1, 0, 1.5],
                                [0, 0, 1, -0.2],
                                [0, 0, 0, 1]])



# For initial position
const_right_wrist_vuer_mat = np.array([[1, 0, 0, 0.245],
                                       [0, 1, 0, 0.26],
                                       [0, 0, 1, -0.2105],
                                       [0, 0, 0, 1]])

# For initial position
const_left_wrist_vuer_mat = np.array([[1, 0, 0, 0.245],
                                      [0, 1, 0, -0.26],
                                      [0, 0, 1, -0.2105],
                                      [0, 0, 0, 1]])

# base坐标系转换
# (基础)OpenXR 坐标系: Y 轴向上, Z 轴向后, X 轴向右
# (基础)Robot 坐标系:  Z 轴向上, Y 轴向左, X 轴向前
T_robot_openxr = np.array([
    [ 0,  0, -1, 0],  # Robot_X = -OpenXR_Z
    [-1,  0,  0, 0],  # Robot_Y = -OpenXR_X
    [ 0,  1,  0, 0],  # Robot_Z =  OpenXR_Y
    [ 0,  0,  0, 1]
])

# 手部坐标系转换(绕x轴旋转-90度)
T_to_urdf_wrist = np.array([
    [1,  0,  0,  0],
    [0,  0,  1,  0],
    [0, -1,  0,  0],
    [0,  0,  0,  1]
])

T_to_inspire_hand = np.array([[0, 0, 1, 0],
                              [-1,0, 0, 0],
                              [0, -1,0, 0],
                              [0, 0, 0, 1]])