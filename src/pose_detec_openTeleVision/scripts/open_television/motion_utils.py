import numpy as np

def mat_update(prev_mat, mat):
    if np.linalg.det(mat) == 0:
        return prev_mat, False
    else:
        return mat, True


def fast_mat_inv(mat):
    ret = np.eye(4)
    ret[:3, :3] = mat[:3, :3].T
    ret[:3, 3] = -mat[:3, :3].T @ mat[:3, 3]
    return ret

# 用于替换 trimesh.transformations 模块中的 rotation_matrix 函数
def patched_rotation_matrix(angle, direction, point=None):
    """Return matrix to rotate about axis defined by point and direction.

    Parameters
    -------------
    angle     : float, or sympy.Symbol
      Angle, in radians or symbolic angle
    direction : (3,) float
      Any vector along rotation axis
    point     : (3, ) float, or None
      Origin point of rotation axis

    Returns
    -------------
    matrix : (4, 4) float, or (4, 4) sympy.Matrix
      Homogeneous transformation matrix
    """
    import numpy as np
    import math

    # 确保 direction 是一个 numpy 数组并且形状正确
    try:
        direction = np.array(direction, dtype=float)
        if direction.shape != (3,):
            if len(direction) == 3:
                direction = direction.reshape(3)
            else:
                # 如果 direction 不是一个有效的向量，使用默认值
                direction = np.array([0, 0, 1], dtype=float)
    except:
        # 如果转换失败，使用默认值
        direction = np.array([0, 0, 1], dtype=float)

    sina = math.sin(angle)
    cosa = math.cos(angle)
    direction = direction / np.linalg.norm(direction)

    # rotation matrix around unit vector
    R = np.diag([cosa, cosa, cosa])
    R += np.outer(direction, direction) * (1.0 - cosa)
    direction *= sina
    R += np.array([[0.0, -direction[2], direction[1]],
                   [direction[2], 0.0, -direction[0]],
                   [-direction[1], direction[0], 0.0]])
    M = np.identity(4)
    M[:3, :3] = R

    if point is not None:
        # rotation not around origin
        point = np.array(point[:3], dtype=float, copy=False)
        M[:3, 3] = point - np.dot(R, point)

    return M