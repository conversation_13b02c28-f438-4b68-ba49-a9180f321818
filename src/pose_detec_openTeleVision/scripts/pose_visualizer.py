#!/usr/bin/env python3

import rospy
import numpy as np
import matplotlib
# 设置 Matplotlib 后端，确保交互功能正常工作
matplotlib.use('TkAgg')
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D
from geometry_msgs.msg import PoseStamped
import tf
import threading
import time

class PoseVisualizer:
    def __init__(self):
        # Initialize ROS node
        rospy.init_node('pose_visualizer', anonymous=True)

        # Create lock for thread safety
        self.lock = threading.Lock()

        # Initialize pose data
        self.left_pose = None
        self.right_pose = None

        # Subscribe to pose topics
        rospy.Subscriber('/left_hand_pose', PoseStamped, self.left_pose_callback)
        rospy.Subscriber('/right_hand_pose', PoseStamped, self.right_pose_callback)

        # Setup figure with interactive mode
        plt.ion()  # Interactive mode
        self.fig = plt.figure(figsize=(10, 8))
        self.ax = self.fig.add_subplot(111, projection='3d')

        # Enable mouse rotation and zooming
        self.fig.canvas.manager.set_window_title('Hand Pose Visualization')
        self.ax.mouse_init()

        # Set axis labels
        self.ax.set_xlabel('X')
        self.ax.set_ylabel('Y')
        self.ax.set_zlabel('Z')

        # Set fixed axis limits
        self.ax.set_xlim([-0.5, 0.5])
        self.ax.set_ylim([-0.5, 0.5])
        self.ax.set_zlim([-0.5, 0.5])

        # Set title
        self.ax.set_title('Hand Pose Visualization')

        # Initialize points and coordinate systems
        self.left_point = self.ax.scatter([], [], [], c='r', marker='o', s=100, label='Left Hand')
        self.right_point = self.ax.scatter([], [], [], c='b', marker='o', s=100, label='Right Hand')

        # Initialize coordinate system arrows
        self.left_arrows = []
        self.right_arrows = []

        # Initialize text objects for coordinates
        self.left_text = None
        self.right_text = None

        # Add legend
        self.ax.legend()

        # Show grid
        self.ax.grid(True)

        # Set initial view angle
        self.ax.view_init(elev=30, azim=45)



    def left_pose_callback(self, msg):
        """Left hand pose callback"""
        with self.lock:
            self.left_pose = msg

    def right_pose_callback(self, msg):
        """Right hand pose callback"""
        with self.lock:
            self.right_pose = msg

    def quaternion_to_rotation_matrix(self, quaternion):
        """Convert quaternion to rotation matrix"""
        q = [quaternion.x, quaternion.y, quaternion.z, quaternion.w]
        return tf.transformations.quaternion_matrix(q)[:3, :3]

    def draw_coordinate_system(self, position, orientation, color, scale=0.1):
        """Draw coordinate system"""
        # Get rotation matrix
        R = self.quaternion_to_rotation_matrix(orientation)

        # Create axis direction vectors
        x_axis = R[:, 0] * scale
        y_axis = R[:, 1] * scale
        z_axis = R[:, 2] * scale

        # Draw coordinate axes
        arrows = []
        arrows.append(self.ax.quiver(position.x, position.y, position.z,
                                     x_axis[0], x_axis[1], x_axis[2],
                                     color='r', linewidth=2))
        arrows.append(self.ax.quiver(position.x, position.y, position.z,
                                     y_axis[0], y_axis[1], y_axis[2],
                                     color='g', linewidth=2))
        arrows.append(self.ax.quiver(position.x, position.y, position.z,
                                     z_axis[0], z_axis[1], z_axis[2],
                                     color='b', linewidth=2))
        return arrows

    def update_visualization(self):
        """Update visualization"""
        with self.lock:
            # Clear all text objects by completely clearing the axes and redrawing
            self.ax.clear()

            # Reset basic properties after clearing
            self.ax.set_xlabel('X')
            self.ax.set_ylabel('Y')
            self.ax.set_zlabel('Z')
            self.ax.set_title('Hand Pose Visualization')

            # Set fixed axis limits
            self.ax.set_xlim([-0.2, 0.7])
            self.ax.set_ylim([-0.5, 0.5])
            self.ax.set_zlim([-0.2, 0.2])
            self.ax.grid(True)

            # Reinitialize scatter plots
            self.left_point = self.ax.scatter([], [], [], c='r', marker='o', s=100, label='Left Hand')
            self.right_point = self.ax.scatter([], [], [], c='b', marker='o', s=100, label='Right Hand')

            # Add legend
            self.ax.legend()

            if self.left_pose is not None:
                # Update left hand point position
                self.left_point._offsets3d = ([self.left_pose.pose.position.x],
                                             [self.left_pose.pose.position.y],
                                             [self.left_pose.pose.position.z])

                # Clear old coordinate system
                for arrow in self.left_arrows:
                    if arrow in self.ax.collections:
                        arrow.remove()

                # Draw new coordinate system
                self.left_arrows = self.draw_coordinate_system(
                    self.left_pose.pose.position,
                    self.left_pose.pose.orientation,
                    'r'
                )

                # Display coordinate information
                self.left_text = self.ax.text(self.left_pose.pose.position.x,
                                            self.left_pose.pose.position.y,
                                            self.left_pose.pose.position.z + 0.1,
                                            f'Left: ({self.left_pose.pose.position.x:.2f}, {self.left_pose.pose.position.y:.2f}, {self.left_pose.pose.position.z:.2f})',
                                            color='r')

            if self.right_pose is not None:
                # Update right hand point position
                self.right_point._offsets3d = ([self.right_pose.pose.position.x],
                                              [self.right_pose.pose.position.y],
                                              [self.right_pose.pose.position.z])

                # Clear old coordinate system
                for arrow in self.right_arrows:
                    if arrow in self.ax.collections:
                        arrow.remove()

                # Draw new coordinate system
                self.right_arrows = self.draw_coordinate_system(
                    self.right_pose.pose.position,
                    self.right_pose.pose.orientation,
                    'b'
                )

                # Display coordinate information
                self.right_text = self.ax.text(self.right_pose.pose.position.x,
                                             self.right_pose.pose.position.y,
                                             self.right_pose.pose.position.z + 0.1,
                                             f'Right: ({self.right_pose.pose.position.x:.2f}, {self.right_pose.pose.position.y:.2f}, {self.right_pose.pose.position.z:.2f})',
                                             color='b')



            # 不再强制设置视角，允许用户通过鼠标拖动调整视角

            # Refresh figure
            self.fig.canvas.draw_idle()
            self.fig.canvas.flush_events()



    def run(self):
        """Run visualization loop"""
        rate = rospy.Rate(10)  # 10Hz
        try:
            while not rospy.is_shutdown():
                self.update_visualization()
                rate.sleep()
        except KeyboardInterrupt:
            print("Visualization interrupted by user")
        except Exception as e:
            print(f"Visualization error: {e}")
        finally:
            plt.close(self.fig)

if __name__ == '__main__':
    try:
        visualizer = PoseVisualizer()
        visualizer.run()
    except rospy.ROSInterruptException:
        pass
