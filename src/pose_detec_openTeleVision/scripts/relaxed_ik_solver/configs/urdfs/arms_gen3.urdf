<?xml version="1.0" encoding="utf-8"?>
<!-- This URDF was automatically created by SolidWorks to URDF Exporter! Originally created by <PERSON> (<EMAIL>) 
     Commit Version: 1.6.0-4-g7f85cfe  Build Version: 1.6.7995.38578
     For more information, please see http://wiki.ros.org/sw_urdf_exporter -->
<robot
  name="AA025040901">
  <link name="footprint">
    <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <box
          size="0.1 0.1 0.1" />
      </geometry>
      <material
        name="">
        <color
          rgba="0 0 0 0" />
      </material>
    </visual>
  </link>
  <link
    name="base_link">
    <inertial>
      <origin
        xyz="-0.0137906099453444 0.00103375326981413 -0.563088704955717"
        rpy="0 0 0" />
      <mass
        value="54.770449857177" />
      <inertia
        ixx="0.255174678928375"
        ixy="-2.02065579522071E-05"
        ixz="-0.00334466506072113"
        iyy="0.233021441171162"
        iyz="-6.67680140743922E-05"
        izz="0.135494765980168" />
    </inertial>
    <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://qj2_arm_ctrl/model/meshes_arm_gen3/base_link.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="0.752941176470588 0.752941176470588 0.752941176470588 1" />
      </material>
    </visual>
    <collision>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://qj2_arm_ctrl/model/meshes_arm_gen3/base_link.STL" />
      </geometry>
    </collision>
  </link>
  <joint
    name="base_joint"
    type="fixed">
    <origin
      xyz="0 0 1.45"
      rpy="0 0 0" />
    <parent
      link="footprint" />
    <child
      link="base_link" />
  </joint>
  <link
    name="AR1">
    <inertial>
      <origin
        xyz="0.0083594 9.2398E-05 0.062254"
        rpy="0 0 0" />
      <mass
        value="1.0518" />
      <inertia
        ixx="0.00093105"
        ixy="5.3362E-06"
        ixz="1.9958E-05"
        iyy="0.001001"
        iyz="-9.6291E-07"
        izz="0.00090721" />
    </inertial>
    <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://qj2_arm_ctrl/model/meshes_arm_gen3/AR1.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="0.89804 0.91765 0.92941 1" />
      </material>
    </visual>
    <collision>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://qj2_arm_ctrl/model/meshes_arm_gen3/AR1.STL" />
      </geometry>
    </collision>
  </link>
  <joint
    name="AJR1"
    type="revolute">
    <origin
      xyz="0 -0.193 0.055"
      rpy="1.5708 0 0" />
    <parent
      link="base_link" />
    <child
      link="AR1" />
    <axis
      xyz="0 0 1" />
    <limit lower="-3.14" upper="3.14" effort="100" velocity="1" />
  </joint>
  <link
    name="AR2">
    <inertial>
      <origin
        xyz="0.031266 -8.259E-06 -0.050803"
        rpy="0 0 0" />
      <mass
        value="0.28432" />
      <inertia
        ixx="0.00034291"
        ixy="-1.583E-08"
        ixz="6.9476E-05"
        iyy="0.00040808"
        iyz="4.9468E-09"
        izz="0.00036954" />
    </inertial>
    <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://qj2_arm_ctrl/model/meshes_arm_gen3/AR2.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="0.89804 0.91765 0.92941 1" />
      </material>
    </visual>
    <collision>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://qj2_arm_ctrl/model/meshes_arm_gen3/AR2.STL" />
      </geometry>
    </collision>
  </link>
  <joint
    name="AJR2"
    type="revolute">
    <origin
      xyz="0.0565 0 0.067"
      rpy="-1.5708 0 -1.5708" />
    <parent
      link="AR1" />
    <child
      link="AR2" />
    <axis
      xyz="0 0 1" />
    <limit lower="-2.356194490192345" upper="0.08726646259971647" effort="100" velocity="1" />
  </joint>
  <link
    name="AR3">
    <inertial>
      <origin
        xyz="-0.0033232 3.5802E-06 0.1286"
        rpy="0 0 0" />
      <mass
        value="2.3857" />
      <inertia
        ixx="0.0022934"
        ixy="-3.7749E-06"
        ixz="-2.9436E-05"
        iyy="0.0024369"
        iyz="-4.0989E-06"
        izz="0.0016228" />
    </inertial>
    <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://qj2_arm_ctrl/model/meshes_arm_gen3/AR3.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="0.64706 0.61961 0.58824 1" />
      </material>
    </visual>
    <collision>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://qj2_arm_ctrl/model/meshes_arm_gen3/AR3.STL" />
      </geometry>
    </collision>
  </link>
  <joint
    name="AJR3"
    type="revolute">
    <origin
      xyz="0.059 0 -0.0565"
      rpy="1.5708 0 1.5708" />
    <parent
      link="AR2" />
    <child
      link="AR3" />
    <axis
      xyz="0 0 1" />
    <limit lower="-1.5882496" upper="1.5882496" effort="100" velocity="1" />
  </joint>
  <link
    name="AR4">
    <inertial>
      <origin
        xyz="-0.00058026 -0.094709 -0.042737"
        rpy="0 0 0" />
      <mass
        value="1.2408" />
      <inertia
        ixx="0.0008869"
        ixy="4.2756E-06"
        ixz="-9.5056E-07"
        iyy="0.00054498"
        iyz="-2.4428E-05"
        izz="0.00085884" />
    </inertial>
    <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://qj2_arm_ctrl/model/meshes_arm_gen3/AR4.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="0.89804 0.91765 0.92941 1" />
      </material>
    </visual>
    <collision>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://qj2_arm_ctrl/model/meshes_arm_gen3/AR4.STL" />
      </geometry>
    </collision>
  </link>
  <joint
    name="AJR4"
    type="revolute">
    <origin
      xyz="-0.0424 0 0.186"
      rpy="-1.5708 0 1.5708" />
    <parent
      link="AR3" />
    <child
      link="AR4" />
    <axis
      xyz="0 0 1" />
    <limit lower="-0.0174532925199433" upper="1.93715469" effort="100" velocity="1" />
  </joint>
  <link
    name="AR5">
    <inertial>
      <origin
        xyz="-1.803E-05 0.0011913 0.034704"
        rpy="0 0 0" />
      <mass
        value="0.99941" />
      <inertia
        ixx="0.00046967"
        ixy="-5.4165E-07"
        ixz="-5.0766E-07"
        iyy="0.00038226"
        iyz="-1.8522E-06"
        izz="0.00046884" />
    </inertial>
    <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://qj2_arm_ctrl/model/meshes_arm_gen3/AR5.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="0.89804 0.91765 0.92941 1" />
      </material>
    </visual>
    <collision>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://qj2_arm_ctrl/model/meshes_arm_gen3/AR5.STL" />
      </geometry>
    </collision>
  </link>
  <joint
    name="AJR5"
    type="revolute">
    <origin
      xyz="0 -0.14 -0.0424"
      rpy="1.5708 0 0" />
    <parent
      link="AR4" />
    <child
      link="AR5" />
    <axis
      xyz="0 0 1" />
    <limit lower="-3.14" upper="3.14" effort="100" velocity="1" />
  </joint>
  <link
    name="AR6">
    <inertial>
      <origin
        xyz="0.0013138 -0.06797 -0.033214"
        rpy="0 0 0" />
      <mass
        value="1.0311" />
      <inertia
        ixx="0.00038835"
        ixy="2.3961E-07"
        ixz="-7.225E-09"
        iyy="0.00046996"
        iyz="-8.9045E-06"
        izz="0.00048276" />
    </inertial>
    <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://qj2_arm_ctrl/model/meshes_arm_gen3/AR6.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="0.89804 0.91765 0.92941 1" />
      </material>
    </visual>
    <collision>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://qj2_arm_ctrl/model/meshes_arm_gen3/AR6.STL" />
      </geometry>
    </collision>
  </link>
  <joint
    name="AJR6"
    type="revolute">
    <origin
      xyz="0 0.0345 0.035"
      rpy="-1.5708 0 0" />
    <parent
      link="AR5" />
    <child
      link="AR6" />
    <axis
      xyz="0 0 1" />
    <limit lower="-1.5707963267949" upper="1.5707963267949" effort="100" velocity="1" />
  </joint>
  <link
    name="AR7">
    <inertial>
      <origin
        xyz="0.088117 0.0039171 -0.025519"
        rpy="0 0 0" />
      <mass
        value="0.25759" />
      <inertia
        ixx="9.2168E-05"
        ixy="1.4092E-06"
        ixz="9.7545E-06"
        iyy="0.00014552"
        iyz="7.771E-07"
        izz="0.00012154" />
    </inertial>
    <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://qj2_arm_ctrl/model/meshes_arm_gen3/AR7.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="0.89804 0.91765 0.92941 1" />
      </material>
    </visual>
    <collision>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://qj2_arm_ctrl/model/meshes_arm_gen3/AR7.STL" />
      </geometry>
    </collision>
  </link>
  <joint
    name="AJR7"
    type="revolute">
    <origin
      xyz="0.0345 -0.07 -0.0345"
      rpy="-1.5708 0 -1.5708" />
    <parent
      link="AR6" />
    <child
      link="AR7" />
    <axis
      xyz="0 0 1" />
    <limit lower="-1.5707963267949" upper="1.5707963267949" effort="100" velocity="1" />
  </joint>
  <link
    name="AL1">
    <inertial>
      <origin
        xyz="0.0083594 -9.2399E-05 -0.062254"
        rpy="0 0 0" />
      <mass
        value="1.0518" />
      <inertia
        ixx="0.00093105"
        ixy="-5.3361E-06"
        ixz="-1.9958E-05"
        iyy="0.001001"
        iyz="-9.6284E-07"
        izz="0.00090721" />
    </inertial>
    <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://qj2_arm_ctrl/model/meshes_arm_gen3/AL1.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="0.89804 0.91765 0.92941 1" />
      </material>
    </visual>
    <collision>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://qj2_arm_ctrl/model/meshes_arm_gen3/AL1.STL" />
      </geometry>
    </collision>
  </link>
  <joint
    name="AJL1"
    type="revolute">
    <origin
      xyz="0 0.193 0.055"
      rpy="1.5708 0 0" />
    <parent
      link="base_link" />
    <child
      link="AL1" />
    <axis
      xyz="0 0 1" />
    <limit lower="-3.14" upper="3.14" effort="100" velocity="1" />
  </joint>
  <link
    name="AL2">
    <inertial>
      <origin
        xyz="0.031266 8.259E-06 0.050803"
        rpy="0 0 0" />
      <mass
        value="0.28432" />
      <inertia
        ixx="0.00034291"
        ixy="1.5831E-08"
        ixz="-6.9476E-05"
        iyy="0.00040808"
        iyz="4.947E-09"
        izz="0.00036954" />
    </inertial>
    <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://qj2_arm_ctrl/model/meshes_arm_gen3/AL2.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="0.89804 0.91765 0.92941 1" />
      </material>
    </visual>
    <collision>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://qj2_arm_ctrl/model/meshes_arm_gen3/AL2.STL" />
      </geometry>
    </collision>
  </link>
  <joint
    name="AJL2"
    type="revolute">
    <origin
      xyz="0.0565 0 -0.067"
      rpy="1.5708 0 -1.5708" />
    <parent
      link="AL1" />
    <child
      link="AL2" />
    <axis
      xyz="0 0 1" />
    <limit lower="-2.356194490192345" upper="0.08726646259971647" effort="100" velocity="1" />
  </joint>
  <link
    name="AL3">
    <inertial>
      <origin
        xyz="-0.0033246 -7.7807E-05 -0.1286"
        rpy="0 0 0" />
      <mass
        value="2.3857" />
      <inertia
        ixx="0.0022934"
        ixy="3.7749E-06"
        ixz="2.9236E-05"
        iyy="0.0024369"
        iyz="-4.9066E-06"
        izz="0.0016228" />
    </inertial>
    <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://qj2_arm_ctrl/model/meshes_arm_gen3/AL3.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="0.64706 0.61961 0.58824 1" />
      </material>
    </visual>
    <collision>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://qj2_arm_ctrl/model/meshes_arm_gen3/AL3.STL" />
      </geometry>
    </collision>
  </link>
  <joint
    name="AJL3"
    type="revolute">
    <origin
      xyz="0.059 0 0.0565"
      rpy="-1.5708 0 1.5708" />
    <parent
      link="AL2" />
    <child
      link="AL3" />
    <axis
      xyz="0 0 1" />
    <limit lower="-1.5882496" upper="1.5882496" effort="100" velocity="1" />
  </joint>
  <link
    name="AL4">
    <inertial>
      <origin
        xyz="-0.00058038 -0.094708 0.042703"
        rpy="0 0 0" />
      <mass
        value="1.2408" />
      <inertia
        ixx="0.00088684"
        ixy="4.2769E-06"
        ixz="1.6585E-06"
        iyy="0.000545"
        iyz="2.5663E-05"
        izz="0.0008587" />
    </inertial>
    <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://qj2_arm_ctrl/model/meshes_arm_gen3/AL4.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="0.89804 0.91765 0.92941 1" />
      </material>
    </visual>
    <collision>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://qj2_arm_ctrl/model/meshes_arm_gen3/AL4.STL" />
      </geometry>
    </collision>
  </link>
  <joint
    name="AJL4"
    type="revolute">
    <origin
      xyz="-0.0424 0 -0.186"
      rpy="1.5708 0 1.5708" />
    <parent
      link="AL3" />
    <child
      link="AL4" />
    <axis
      xyz="0 0 1" />
    <limit lower="-0.0174532925199433" upper="1.93715469" effort="100" velocity="1" />
  </joint>
  <link
    name="AL5">
    <inertial>
      <origin
        xyz="1.803E-05 0.0011913 -0.034704"
        rpy="0 0 0" />
      <mass
        value="0.99941" />
      <inertia
        ixx="0.00046967"
        ixy="5.4165E-07"
        ixz="-5.0766E-07"
        iyy="0.00038226"
        iyz="1.8522E-06"
        izz="0.00046884" />
    </inertial>
    <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://qj2_arm_ctrl/model/meshes_arm_gen3/AL5.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="0.89804 0.91765 0.92941 1" />
      </material>
    </visual>
    <collision>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://qj2_arm_ctrl/model/meshes_arm_gen3/AL5.STL" />
      </geometry>
    </collision>
  </link>
  <joint
    name="AJL5"
    type="revolute">
    <origin
      xyz="0 -0.14 0.0424"
      rpy="-1.5708 0 0" />
    <parent
      link="AL4" />
    <child
      link="AL5" />
    <axis
      xyz="0 0 1" />
    <limit lower="-3.14" upper="3.14" effort="100" velocity="1" />
  </joint>
  <link
    name="AL6">
    <inertial>
      <origin
        xyz="0.0013138 -0.067927 0.033214"
        rpy="0 0 0" />
      <mass
        value="1.0311" />
      <inertia
        ixx="0.00038835"
        ixy="1.5406E-06"
        ixz="7.268E-09"
        iyy="0.00046996"
        iyz="8.5446E-06"
        izz="0.00048276" />
    </inertial>
    <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://qj2_arm_ctrl/model/meshes_arm_gen3/AL6.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="0.89804 0.91765 0.92941 1" />
      </material>
    </visual>
    <collision>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://qj2_arm_ctrl/model/meshes_arm_gen3/AL6.STL" />
      </geometry>
    </collision>
  </link>
  <joint
    name="AJL6"
    type="revolute">
    <origin
      xyz="0 0.0345 -0.035"
      rpy="1.5708 0 0" />
    <parent
      link="AL5" />
    <child
      link="AL6" />
    <axis
      xyz="0 0 1" />
    <limit lower="-1.5707963267949" upper="1.5707963267949" effort="100" velocity="1" />
  </joint>
  <link
    name="AL7">
    <inertial>
      <origin
        xyz="0.084008 0.0034769 0.024486"
        rpy="0 0 0" />
      <mass
        value="0.24463" />
      <inertia
        ixx="9.1501E-05"
        ixy="1.6212E-06"
        ixz="-9.5005E-06"
        iyy="0.00014416"
        iyz="-7.1217E-07"
        izz="0.00011899" />
    </inertial>
    <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://qj2_arm_ctrl/model/meshes_arm_gen3/AL7.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="0.89804 0.91765 0.92941 1" />
      </material>
    </visual>
    <collision>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://qj2_arm_ctrl/model/meshes_arm_gen3/AL7.STL" />
      </geometry>
    </collision>
  </link>
  <joint
    name="AJL7"
    type="revolute">
    <origin
      xyz="0.0345 -0.07 0.0345"
      rpy="1.5708 0 -1.5708" />
    <parent
      link="AL6" />
    <child
      link="AL7" />
    <axis
      xyz="0 0 1" /> 
    <limit lower="-1.5707963267949" upper="1.5707963267949" effort="100" velocity="1" />
  </joint>
</robot>