#! /usr/bin/env python3

import os
import rospkg
import rospy
import sys
import yaml
import numpy as np
import time

from robot import Robot
from python_wrapper import RelaxedIKRust

class RelaxedIK:
    def __init__(self, side):
        # rospy.sleep(1)
        path_to_src = rospkg.RosPack().get_path('pose_detec_openTeleVision')
        setting_file_path = path_to_src + '/scripts/relaxed_ik_solver/configs/' + side + '_arm_settings.yaml'

        os.chdir(path_to_src + '/scripts/relaxed_ik_solver')

        # Load the infomation
        
        print("setting_file_path: ", setting_file_path)
        setting_file = open(setting_file_path, 'r')
        settings = yaml.load(setting_file, Loader=yaml.FullLoader)

        urdf_file_path = path_to_src + '/scripts/relaxed_ik_solver/configs/urdfs/' + settings["urdf"]
        print("urdf_file_path: ", urdf_file_path)
        urdf_file = open(urdf_file_path, 'r')
        urdf_string = urdf_file.read()
        rospy.set_param('robot_description', urdf_string)

        self.relaxed_ik = RelaxedIKRust(setting_file_path)

        self.robot = Robot(setting_file_path)

        print("\nSolver RelaxedIK initialized!\n")

if __name__ == '__main__':
    rospy.init_node('relaxed_ik')
    relaxed_ik_R = RelaxedIK('right')
    relaxed_ik_L = RelaxedIK('left')

    # 示例末端执行器目标位姿（单个末端）
    test_positions = [0.5, -0.1, 0.5]  # x, y, z
    test_orientations = [0.0, 0.0, 0.0, 1.0]  # 四元数 x, y, z, w
    test_tolerances = [0.0] * 6  # 可选

    for i in range(1):
        start = time.perf_counter()
        ik_solution_R = relaxed_ik_R.relaxed_ik.solve_position(
            test_positions, test_orientations, test_tolerances
        )
        end = time.perf_counter()
        print(f"运行时间: {(end - start)*1000:.6f} 毫秒")
    print("Joint solution: ", np.rad2deg(ik_solution_R))
    start = time.perf_counter()
    ik_solution_L = relaxed_ik_L.relaxed_ik.solve_position(
        [0.5, 0.1, 0.5], [-1,0,0,0], test_tolerances
    )
    end = time.perf_counter()
    print(f"运行时间: {(end - start)*1000:.6f} 毫秒")
    print("Joint solution: ", np.rad2deg(ik_solution_L))
