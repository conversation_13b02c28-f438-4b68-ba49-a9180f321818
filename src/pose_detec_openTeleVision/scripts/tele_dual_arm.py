#!/usr/bin/env python3

import math
import numpy as np
import torch
import cv2

from open_television.TeleVision import OpenTeleVision
from open_television.Preprocessor import VuerPreprocessor
from open_television.constants_vuer import tip_indices
from dex_retargeting.retargeting_config import RetargetingConfig
from pytransform3d import rotations

from open_television.image_server.image_server import ImageServer
from open_television.image_server.image_client import ImageClient

from pathlib import Path
import argparse
import time
import yaml
from multiprocessing import Array, Process, shared_memory, Queue, Manager, Event, Semaphore
import threading

import rospy
import rospkg
path_to_src = rospkg.RosPack().get_path('pose_detec_openTeleVision') + '/scripts'

import os
import sys

# 修复 trimesh.transformations.rotation_matrix 函数的问题
import trimesh.transformations
import types
from open_television.motion_utils import patched_rotation_matrix
# 替换原始函数
trimesh.transformations.rotation_matrix = patched_rotation_matrix

# 该文件实现了基于Vuer的手部远程操作系统，包括VR手部追踪和物理模拟环境
class VuerTeleop:
    """
    VuerTeleop类负责处理VR手部追踪数据并将其转换为机器人手的控制信号
    """
    def __init__(self, config_file_path):
        # 相机服务端启动
        config = {
            'fps': 30,
            'head_camera_type': 'realsense',
            'head_camera_image_shape': [480, 640],  # 提高分辨率到HD (720p)
            'head_camera_id_numbers': ["335222076022"]
        }
        self.img_server = ImageServer(config, Unit_Test=False)
        self.image_server_thread = threading.Thread(target=self.img_server.send_process, daemon=True)
        self.image_server_thread.start()

        # 创建图像相关资源
        img_shape = (480, 640, 3)  # 图像形状：高度720，宽度1280 (640*2)，3个颜色通道
        # 创建共享内存用于存储图像数据
        self.img_shm = shared_memory.SharedMemory(create=True, size=np.prod(img_shape) * np.uint8().itemsize)
        img_array = np.ndarray(img_shape, dtype=np.uint8, buffer=self.img_shm.buf)
        # 创建图像客户端
        self.img_client = ImageClient(tv_img_shape=img_shape, tv_img_shm_name=self.img_shm.name, image_show=False, port=5555)
        # 在后台线程中接收图像
        self.image_client_thread = threading.Thread(target=self.img_client.receive_process, daemon=True)
        self.image_client_thread.start()

        """
        初始化VuerTeleop对象

        参数:
            config_file_path: 包含手部重定向配置的YAML文件路径
        """
        # 初始化OpenTeleVision和VuerPreprocessor
        self.tv = OpenTeleVision(False, img_shape, self.img_shm.name, path_to_src + "/open_television/pem/cert.pem", path_to_src + "/open_television/pem/key.pem")
        self.processor = VuerPreprocessor()

        # 加载手部重定向配置
        RetargetingConfig.set_default_urdf_dir('.')
        with Path(config_file_path).open('r') as f:
            cfg = yaml.safe_load(f)
        left_retargeting_config = RetargetingConfig.from_dict(cfg['left'])
        right_retargeting_config = RetargetingConfig.from_dict(cfg['right'])
        self.left_retargeting = left_retargeting_config.build()
        self.right_retargeting = right_retargeting_config.build()

    def step(self):
        """
        处理一帧VR追踪数据并计算手部控制信号

        返回:
            head_rmat: 头部旋转矩阵
            left_pose: 左手位姿（位置和四元数）
            right_pose: 右手位姿（位置和四元数）
            left_qpos: 左手关节角度
            right_qpos: 右手关节角度
        """
        # 从处理器获取头部和手部的变换矩阵
        head_mat, left_wrist_mat, right_wrist_mat, left_hand_mat, right_hand_mat = self.processor.process(self.tv)

        # 提取头部旋转矩阵
        head_rmat = head_mat[:3, :3]

        # 计算左手位姿（位置和四元数）
        left_pose = np.concatenate([left_wrist_mat[:3, 3] + np.array([-0.6, 0, 1.6]),  # 位置偏移
                                    rotations.quaternion_from_matrix(left_wrist_mat[:3, :3])[[1, 2, 3, 0]]])  # 旋转四元数

        # 计算右手位姿（位置和四元数）
        right_pose = np.concatenate([right_wrist_mat[:3, 3] + np.array([-0.6, 0, 1.6]),  # 位置偏移
                                     rotations.quaternion_from_matrix(right_wrist_mat[:3, :3])[[1, 2, 3, 0]]])  # 旋转四元数

        # 使用重定向模型计算左手关节角度并重新排序
        left_qpos = self.left_retargeting.retarget(left_hand_mat[tip_indices])[[4, 5, 6, 7, 10, 11, 8, 9, 0, 1, 2, 3]]

        # 使用重定向模型计算右手关节角度并重新排序
        right_qpos = self.right_retargeting.retarget(right_hand_mat[tip_indices])[[4, 5, 6, 7, 10, 11, 8, 9, 0, 1, 2, 3]]

        return head_rmat, left_pose, right_pose, left_qpos, right_qpos

    def end(self):
        """
        清理资源
        """
        # 防止重复关闭
        if hasattr(self, '_closed') and self._closed:
            print("VuerTeleop已经关闭，跳过重复关闭")
            return

        print("正在关闭图像服务器和客户端...")
        # 标记为已关闭
        self._closed = True

        # 首先关闭可能包含Qt计时器的组件
        if hasattr(self, 'img_client') and hasattr(self.img_client, '_image_show') and self.img_client._image_show:
            try:
                # 在主线程中关闭OpenCV窗口
                cv2.destroyAllWindows()
                # 等待一小段时间确保窗口关闭
                for i in range(5):
                    cv2.waitKey(1)
                print("已关闭所有OpenCV窗口")
            except Exception as e:
                print(f"关闭OpenCV窗口时出错: {e}")

        # 关闭OpenTeleVision
        try:
            if hasattr(self, 'tv'):
                self.tv.close()
                print("OpenTeleVision已关闭")
        except Exception as e:
            print(f"关闭OpenTeleVision时出错: {e}")

        # 关闭图像客户端
        try:
            if hasattr(self, 'img_client'):
                self.img_client._close()
                print("图像客户端已关闭")
        except Exception as e:
            print(f"关闭图像客户端时出错: {e}")

        # 关闭图像服务器
        try:
            if hasattr(self, 'img_server'):
                self.img_server._close()
                print("图像服务器已关闭")
        except Exception as e:
            print(f"关闭图像服务器时出错: {e}")

        # 等待线程结束
        if hasattr(self, 'image_server_thread') and self.image_server_thread.is_alive():
            try:
                self.image_server_thread.join(timeout=2.0)  # 等待最多2秒
                print("图像服务器线程已结束")
            except Exception as e:
                print(f"等待图像服务器线程结束时出错: {e}")

        if hasattr(self, 'image_client_thread') and self.image_client_thread.is_alive():
            try:
                self.image_client_thread.join(timeout=2.0)  # 等待最多2秒
                print("图像客户端线程已结束")
            except Exception as e:
                print(f"等待图像客户端线程结束时出错: {e}")

        # 释放共享内存
        if hasattr(self, 'img_shm'):
            try:
                self.img_shm.close()
                self.img_shm.unlink()  # 删除共享内存
                print("共享内存已释放")
            except Exception as e:
                print(f"释放共享内存时出错: {e}")

        print("资源清理完成")



if __name__ == '__main__':
    rospy.init_node('teleop_node')

    """
    主程序入口：创建远程操作和模拟环境，并运行主循环
    """
    current_dir = os.path.dirname(os.path.abspath(__file__))
    print("current_dir: ", current_dir)
    sys.path.append(current_dir)
    # 初始化远程操作器和模拟器
    teleoperator = VuerTeleop(path_to_src + '/open_television/configs/inspire_hand1.yml')
    # simulator = Sim()

    # 添加信号处理
    import signal

    # 定义信号处理函数
    def signal_handler(sig, frame):
        print(f"\n接收到信号 {sig}，正在关闭程序...")
        # 设置关闭标志
        shutdown_requested[0] = True

    # 创建一个共享的关闭标志
    shutdown_requested = [False]

    # 注册信号处理器
    signal.signal(signal.SIGINT, signal_handler)  # Ctrl+C
    signal.signal(signal.SIGTERM, signal_handler) # 终止信号

    # 添加超时函数
    def timeout_function(func, args=(), kwargs={}, timeout_duration=5, default=None):
        """带超时的函数调用"""
        import threading

        class TimeoutError(Exception):
            pass

        result = [default]
        def target():
            try:
                result[0] = func(*args, **kwargs)
            except Exception as e:
                print(f"函数执行出错: {e}")

        thread = threading.Thread(target=target)
        thread.daemon = True
        thread.start()
        thread.join(timeout_duration)
        if thread.is_alive():
            print(f"函数执行超时 ({timeout_duration}秒)")
            return default
        return result[0]

    try:
        # 主循环
        while not rospy.is_shutdown() and not shutdown_requested[0]:
            # 获取手部追踪数据
            head_rmat, left_pose, right_pose, left_qpos, right_qpos = teleoperator.step()
            print("left_pose: ")
            print(left_pose)
            print("right_pose: ")
            print(right_pose)
            # print("right_qpos: ", right_qpos)
            # print("left_pose: ", left_pose)
            # print("right_pose: ", right_pose)
            # 更新模拟环境并获取相机图像
            # left_img, right_img = simulator.step(head_rmat, left_pose, right_pose, left_qpos, right_qpos)
            # 将左右眼图像拼接并复制到共享内存
            # np.copyto(teleoperator.img_array, np.hstack((left_img, right_img)))
            time.sleep(0.03)  # 约33Hz的更新频率
    except KeyboardInterrupt:
        print("\n接收到Ctrl+C，正在关闭程序...")
        shutdown_requested[0] = True
    except Exception as e:
        print(f"\n程序发生异常: {e}")
        shutdown_requested[0] = True
    finally:
        # 无论如何都确保资源被正确释放
        print("正在清理资源...")

        # 使用超时机制调用end()方法
        try:
            # 确保调用end()方法释放资源，设置5秒超时
            timeout_function(teleoperator.end, timeout_duration=5)
        except Exception as e:
            print(f"清理资源时发生错误: {e}")

        # 关闭ROS节点
        try:
            rospy.signal_shutdown("程序结束")
        except Exception as e:
            print(f"关闭ROS节点时发生错误: {e}")

        print("程序已安全退出")
        # 使用sys.exit而不是exit()
        import sys
        sys.exit(0)

"""
qpos:
0: 食指
2: 中指
4: 小指
6: 无名指
8: 大拇指1
9: 大拇指2
"""