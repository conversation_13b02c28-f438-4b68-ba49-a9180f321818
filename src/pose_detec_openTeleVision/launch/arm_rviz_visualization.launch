<?xml version="1.0"?>
<launch>
  <!-- 加载机器人URDF模型到参数服务器 -->
  <param name="robot_description" textfile="$(find pose_detec_openTeleVision)/scripts/relaxed_ik_solver/configs/urdfs/arms_gen3.urdf" />

  <!-- 启动RViz -->
  <node name="rviz" pkg="rviz" type="rviz" args="-d $(find pose_detec_openTeleVision)/rviz/dual_arm_visualization.rviz" required="true" />

  <!-- 启动机器人状态发布器 -->
  <node name="robot_state_publisher" pkg="robot_state_publisher" type="robot_state_publisher" />

  <!-- 启动位姿可视化器 -->
  <node name="pose_rviz_visualizer" pkg="pose_detec_openTeleVision" type="pose_rviz_visualizer.py" output="screen" />
</launch>
