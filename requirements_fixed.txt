# 修复版本冲突的requirements.txt
# Python 3.9+ 兼容版本

# 核心依赖
aiohttp==3.9.5
aiohttp_cors==0.7.0
aiortc==1.8.0
av==11.0.0

# 机器人控制相关
dynamixel_sdk==3.7.31

# 数学和科学计算
numpy>=1.23.5,<1.26
scipy==1.10.1
scikit_learn==1.3.2

# 深度学习框架
torch==2.3.0
torchvision==0.18.0
tensorflow==2.15.0

# 计算机视觉 (只保留一个opencv包)
opencv_python==*********

# 数据处理
pandas==2.0.3
h5py==3.11.0
PyYAML==6.0.1

# 可视化
matplotlib==3.7.5
seaborn==0.13.2

# 开发工具
ipython==8.12.3
tqdm==4.66.4

# 3D和几何计算
pytransform3d==3.5.0
einops==0.8.0

# 实验和日志
wandb==0.17.3

# 工具包
packaging==24.1
setuptools==69.5.1

# 参数管理
params_proto==2.12.1

# 网络和通信
vuer==0.0.32rc7

# 类型注解 (使用范围版本避免冲突)
typing-extensions>=4.0.0,<5.0.0

# 可选的复杂依赖 (如果需要可以单独安装)
# dex_retargeting==0.4.6  # 注释掉，因为它引入了复杂的依赖冲突

# 兼容性依赖
markdown>=3.3,<3.6
opt-einsum>=3.3,<4.0
pexpect>=4.8,<5.0
Pillow>=9.0,<10.0
