## Introduction

这是启江系列机器人上肢控制代码仓库，在工作空间下包含以下功能包：

- `pose_detec_hand`：手部姿态检测相关代码
- `pose_detec_mediapipe`：mediapipe姿态检测相关代码
- `pose_detec_openTeleVision`：OpenTeleVsion姿态检测相关代码
- `robot_controller`：机器人控制相关代码

## Usage

### 1. 安装依赖

#### 1.1 mediapipe环境

```bash
conda create --name mediapipe python==3.10
conda activate mediapipe
pip install mediapipe
pip install catkin_pkg rospkg pyrealsense2 pyyaml pykalman
pip install netifaces
```

#### 1.2 coal环境

```bash
conda create --name coal python==3.9
conda activate coal
conda install coal -c conda-forge
```

#### 1.3 tv环境

安装OpenTeleVision源码

```bash
git clone https://github.com/OpenTeleVision/TeleVision.git
```

conda虚拟环境创建
```bash
conda create -n tv python=3.8
conda activate tv
cd TeleVision
```

安装所需的库
```bash
pip install -r requirements.txt
```
此处可能存在版本冲突，可以将`requirements.txt`按互斥的库拆分，然后再分别安装
```bash
pip install -r requirements_part1.txt
pip install -r requirements_part2.txt
```

继续安装需要的库
```bash
cd act/detr && pip install -e .
```

```bash
pip install zmq
pip install opencv-python
pip install pyrealsense2
```

conda环境下PyKDL问题：
安装 PyKDL 可能会遇到兼容性或路径问题，因为 ROS 通常使用的是系统 Python 路径，而 conda 环境是独立的。
在 ROS 环境中，PyKDL 通常是通过系统包管理器（如 apt）安装的，而不是通过 pip 或 conda。
我们可以创建一个符号链接，将系统 Python 中的 PyKDL 模块链接到 Conda 环境中。

```bash
find /usr -path "*/dist-packages/*" -name "PyKDL*"
```
输出
`/usr/lib/python3/dist-packages/PyKDL.cpython-38-x86_64-linux-gnu.so`
现在我们找到了系统 Python 中的 PyKDL 模块。让我们创建一个符号链接，将它链接到 Conda 环境中：
```bash
mkdir -p ~/miniconda3/envs/tv/lib/python3.8/site-packages/
ln -s /usr/lib/python3/dist-packages/PyKDL.cpython-38-x86_64-linux-gnu.so ~/miniconda3/envs/tv/lib/python3.8/site-packages/PyKDL.so
```

PyKDL 依赖于 sip 模块，但它在 Conda 环境中不可用。现在需要找到系统中的 sip 模块并链接它：
```bash
find /usr -path "*/dist-packages/*" -name "sip*"
```
输出
```
/usr/lib/python3/dist-packages/sip.cpython-38-x86_64-linux-gnu.so
/usr/lib/python3/dist-packages/sip.pyi
/usr/lib/python3/dist-packages/sipconfig.py
/usr/lib/python3/dist-packages/__pycache__/sipconfig_nd8.cpython-38.pyc
/usr/lib/python3/dist-packages/__pycache__/sipdistutils.cpython-38.pyc
/usr/lib/python3/dist-packages/__pycache__/sipconfig.cpython-38.pyc
/usr/lib/python3/dist-packages/twisted/protocols/__pycache__/sip.cpython-38.pyc
/usr/lib/python3/dist-packages/twisted/protocols/sip.py
/usr/lib/python3/dist-packages/sipdistutils.py
/usr/lib/python3/dist-packages/sip-4.19.21.dist-info
/usr/lib/python3/dist-packages/sipconfig_nd8.py
```
让我们链接系统中的 sip 模块到 Conda 环境：
`ln -s /usr/lib/python3/dist-packages/sip.cpython-38-x86_64-linux-gnu.so ~/miniconda3/envs/tv/lib/python3.8/site-packages/sip.so`

#### 1.4 inspire hand端口配置

```bash
sudo gedit -p /etc/udev/rules.d/70-ttyUSB.rules
```
 
在打开的文件中输入：
 
```bash
KERNEL=="ttyUSB*", OWNER="root", GROUP="root", MODE="0666"
``` 
注意`KERNEL=="ttyUSB*"`,这里的`*`不是串口号就是`*`。

或
```bash
SUBSYSTEM=="tty", ATTRS{idVendor}=="1a86", ATTRS{idProduct}=="7523", MODE="0666", GROUP="dialout"
```

保存关闭之后，重启电脑或执行
```bash
sudo udevadm control --reload-rules
sudo udevadm trigger
```

#### 1.5 手柄配置

##### 查看是否安装驱动

Xbox 手柄通常使用 `xpad` 或微软官方开源驱动 `xpadneo`。

查看是否加载了驱动：
```bash
lsmod | grep xpad
```

如果没有看到 `xpad` 或 `xpadneo`，需要加载驱动。

##### 安装驱动

官方驱动地址：
[https://github.com/atar-axis/xpadneo](https://github.com/atar-axis/xpadneo)

```bash
sudo apt install dkms linux-headers-$(uname -r) git

git clone https://github.com/atar-axis/xpadneo.git
cd xpadneo
sudo ./install.sh
```

安装完成后，重启电脑。

如果不方便重启电脑，则需要重启相关模块：

重新加载 `xpadneo` 模块。安装 `xpadneo` 后，驱动模块通常是 `hid_xpadneo`，尝试：
```bash
sudo modprobe hid_xpadneo
```

如果提示找不到模块，尝试：

```bash
sudo dkms autoinstall
sudo modprobe hid_xpadneo
```

重新启动 `bluetooth` 服务（确保蓝牙服务刷新）

```bash
sudo systemctl restart bluetooth
```

##### 查看驱动是否生效

确认 `hid_xpadneo` 已加载：
```bash
lsmod | grep xpadneo
```

输出
```
hid_xpadneo            36864  0
ff_memless             24576  2 hid_microsoft,hid_xpadneo
hid                   262144  7 hidp,usbhid,hid_microsoft,hid_generic,hid_xpadneo,hid_logitech_dj,hid_logitech_hidpp
```


##### 验证手柄是否可用

用 `python3 -m evdev.evtest` 或者 `jstest-gtk` 选中手柄设备测试按钮响应。

前者需要安装
```bash
pip install evdev
```
若一切顺利则能够找到
```
20  /dev/input/event25   Xbox Wireless Controller            e4:60:17:4d:20:d2                   86:2a:d1:c7:39:73
```

后者需要安装
```bash
sudo apt install jstest-gtk
```
若一切顺利则弹出的GUI会显示手柄设备信息。

##### 控制代码

位于robot_controller功能包下/scripts/joystick_ctrl.py

此代码使用`pygame`库，需要安装

```bash
pip install pygame
```