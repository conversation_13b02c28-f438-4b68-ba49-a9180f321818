## Introduction

这是启江系列机器人上肢控制代码仓库，在工作空间下包含以下功能包：

- `pose_detec_hand`：手部姿态检测相关代码
- `pose_detec_mediapipe`：mediapipe姿态检测相关代码
- `pose_detec_openTeleVision`：OpenTeleVsion姿态检测相关代码
- `robot_controller`：机器人控制相关代码

## Usage

### 1. 安装依赖

#### 1.1 mediapipe环境

```bash
conda create --name mediapipe python==3.10
conda activate mediapipe
pip install mediapipe
pip install catkin_pkg rospkg pyrealsense2 pyyaml pykalman
pip install netifaces
```

#### 1.2 coal环境

```bash
conda create --name coal python==3.9
conda activate coal
conda install coal -c conda-forge
```

#### 1.3 tv环境

安装OpenTeleVision源码

```bash
git clone https://github.com/OpenTeleVision/TeleVision.git
```

conda虚拟环境创建
```bash
conda create -n tv python=3.8
conda activate tv
cd TeleVision
```

安装所需的库
```bash
pip install -r requirements.txt
```
此处可能存在版本冲突，可以将`requirements.txt`按互斥的库拆分，然后再分别安装
```bash
pip install -r requirements_part1.txt
pip install -r requirements_part2.txt
```

继续安装需要的库
```bash
cd act/detr && pip install -e .
```

```bash
pip install zmq
pip install opencv-python
pip install pyrealsense2
```

#### 1.4 inspire hand端口配置

```bash
sudo gedit -p /etc/udev/rules.d/70-ttyUSB.rules
```
 
在打开的文件中输入：
 
```bash
KERNEL=="ttyUSB*", OWNER="root", GROUP="root", MODE="0666"
``` 
注意`KERNEL=="ttyUSB*"`,这里的`*`不是串口号就是`*`。

或
```bash
SUBSYSTEM=="tty", ATTRS{idVendor}=="1a86", ATTRS{idProduct}=="7523", MODE="0666", GROUP="dialout"
```

保存关闭之后，重启电脑或执行
```bash
sudo udevadm control --reload-rules
sudo udevadm trigger
```

