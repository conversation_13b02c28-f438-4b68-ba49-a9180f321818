#!/usr/bin/env sh
# generated from catkin/python/catkin/environment_cache.py

# based on a snapshot of the environment before and after calling the setup script
# it emulates the modifications of the setup script without recurring computations

# new environment variables

# modified environment variables
export CMAKE_PREFIX_PATH="/home/<USER>/workspace/arm_control_ws/devel:$CMAKE_PREFIX_PATH"
export PATH='/opt/ros/noetic/bin:/home/<USER>/miniconda3/condabin:/usr/local/nvidia/bin:/usr/local/cuda/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin'
export PWD='/home/<USER>/workspace/arm_control_ws/build'
export ROSLISP_PACKAGE_DIRECTORIES='/home/<USER>/workspace/arm_control_ws/devel/share/common-lisp'
export ROS_PACKAGE_PATH="/home/<USER>/workspace/arm_control_ws/src:$ROS_PACKAGE_PATH"