# generated from catkin/cmake/em/order_packages.cmake.em

set(CATKIN_ORDERED_PACKAGES "")
set(CATKIN_ORDERED_PACKAGE_PATHS "")
set(CATKIN_ORDERED_PACKAGES_IS_META "")
set(CATKIN_ORDERED_PACKAGES_BUILD_TYPE "")
list(APPEND CATKIN_ORDERED_PACKAGES "senseglove_launch")
list(APPEND CATKIN_ORDERED_PACKAGE_PATHS "pose_detec_hand/src/senseglove/senseglove_launch")
list(APPEND CATKIN_ORDERED_PACKAGES_IS_META "False")
list(APPEND CATKIN_ORDERED_PACKAGES_BUILD_TYPE "catkin")
list(APPEND CATKIN_ORDERED_PACKAGES "senseglove_shared_resources")
list(APPEND CATKIN_ORDERED_PACKAGE_PATHS "pose_detec_hand/src/senseglove/senseglove_shared_resources")
list(APPEND CATKIN_ORDERED_PACKAGES_IS_META "False")
list(APPEND CATKIN_ORDERED_PACKAGES_BUILD_TYPE "catkin")
list(APPEND CATKIN_ORDERED_PACKAGES "senseglove_finger_distance")
list(APPEND CATKIN_ORDERED_PACKAGE_PATHS "pose_detec_hand/src/senseglove/senseglove_finger_distance")
list(APPEND CATKIN_ORDERED_PACKAGES_IS_META "False")
list(APPEND CATKIN_ORDERED_PACKAGES_BUILD_TYPE "catkin")
list(APPEND CATKIN_ORDERED_PACKAGES "hand_link")
list(APPEND CATKIN_ORDERED_PACKAGE_PATHS "pose_detec_hand/src/hand_link")
list(APPEND CATKIN_ORDERED_PACKAGES_IS_META "False")
list(APPEND CATKIN_ORDERED_PACKAGES_BUILD_TYPE "catkin")
list(APPEND CATKIN_ORDERED_PACKAGES "hand_link_right")
list(APPEND CATKIN_ORDERED_PACKAGE_PATHS "pose_detec_hand/src/hand_link_right")
list(APPEND CATKIN_ORDERED_PACKAGES_IS_META "False")
list(APPEND CATKIN_ORDERED_PACKAGES_BUILD_TYPE "catkin")
list(APPEND CATKIN_ORDERED_PACKAGES "pose_detec_mediapipe")
list(APPEND CATKIN_ORDERED_PACKAGE_PATHS "pose_detec_mediapipe/src/pose_mediapipe")
list(APPEND CATKIN_ORDERED_PACKAGES_IS_META "False")
list(APPEND CATKIN_ORDERED_PACKAGES_BUILD_TYPE "catkin")
list(APPEND CATKIN_ORDERED_PACKAGES "pose_detec_openTeleVision")
list(APPEND CATKIN_ORDERED_PACKAGE_PATHS "pose_detec_openTeleVision")
list(APPEND CATKIN_ORDERED_PACKAGES_IS_META "False")
list(APPEND CATKIN_ORDERED_PACKAGES_BUILD_TYPE "catkin")
list(APPEND CATKIN_ORDERED_PACKAGES "robot_controller")
list(APPEND CATKIN_ORDERED_PACKAGE_PATHS "robot_controller")
list(APPEND CATKIN_ORDERED_PACKAGES_IS_META "False")
list(APPEND CATKIN_ORDERED_PACKAGES_BUILD_TYPE "catkin")
list(APPEND CATKIN_ORDERED_PACKAGES "senseglove_haptics")
list(APPEND CATKIN_ORDERED_PACKAGE_PATHS "pose_detec_hand/src/senseglove/senseglove_haptics")
list(APPEND CATKIN_ORDERED_PACKAGES_IS_META "False")
list(APPEND CATKIN_ORDERED_PACKAGES_BUILD_TYPE "catkin")
list(APPEND CATKIN_ORDERED_PACKAGES "inspire_hand")
list(APPEND CATKIN_ORDERED_PACKAGE_PATHS "pose_detec_hand/src/inspire_hand")
list(APPEND CATKIN_ORDERED_PACKAGES_IS_META "False")
list(APPEND CATKIN_ORDERED_PACKAGES_BUILD_TYPE "catkin")
list(APPEND CATKIN_ORDERED_PACKAGES "inspire_hand_right")
list(APPEND CATKIN_ORDERED_PACKAGE_PATHS "pose_detec_hand/src/inspire_hand_right")
list(APPEND CATKIN_ORDERED_PACKAGES_IS_META "False")
list(APPEND CATKIN_ORDERED_PACKAGES_BUILD_TYPE "catkin")
list(APPEND CATKIN_ORDERED_PACKAGES "imu_interface")
list(APPEND CATKIN_ORDERED_PACKAGE_PATHS "pose_detec_hand/src/imu_interface")
list(APPEND CATKIN_ORDERED_PACKAGES_IS_META "False")
list(APPEND CATKIN_ORDERED_PACKAGES_BUILD_TYPE "catkin")
list(APPEND CATKIN_ORDERED_PACKAGES "senseglove_hardware")
list(APPEND CATKIN_ORDERED_PACKAGE_PATHS "pose_detec_hand/src/hardware_interface/senseglove_hardware")
list(APPEND CATKIN_ORDERED_PACKAGES_IS_META "False")
list(APPEND CATKIN_ORDERED_PACKAGES_BUILD_TYPE "catkin")
list(APPEND CATKIN_ORDERED_PACKAGES "senseglove_hardware_builder")
list(APPEND CATKIN_ORDERED_PACKAGE_PATHS "pose_detec_hand/src/hardware_interface/senseglove_hardware_builder")
list(APPEND CATKIN_ORDERED_PACKAGES_IS_META "False")
list(APPEND CATKIN_ORDERED_PACKAGES_BUILD_TYPE "catkin")
list(APPEND CATKIN_ORDERED_PACKAGES "senseglove_description")
list(APPEND CATKIN_ORDERED_PACKAGE_PATHS "pose_detec_hand/src/senseglove/senseglove_description")
list(APPEND CATKIN_ORDERED_PACKAGES_IS_META "False")
list(APPEND CATKIN_ORDERED_PACKAGES_BUILD_TYPE "catkin")
list(APPEND CATKIN_ORDERED_PACKAGES "senseglove_hardware_interface")
list(APPEND CATKIN_ORDERED_PACKAGE_PATHS "pose_detec_hand/src/hardware_interface/senseglove_hardware_interface")
list(APPEND CATKIN_ORDERED_PACKAGES_IS_META "False")
list(APPEND CATKIN_ORDERED_PACKAGES_BUILD_TYPE "catkin")

set(CATKIN_MESSAGE_GENERATORS )

set(CATKIN_METAPACKAGE_CMAKE_TEMPLATE "/usr/lib/python3/dist-packages/catkin_pkg/templates/metapackage.cmake.in")
