# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.23

robot_controller/CMakeFiles/task02_iron_shirt.dir/src/cmg_tasks/task02_iron_shirt.cpp.o
 /home/<USER>/workspace/arm_control_ws/src/robot_controller/src/cmg_tasks/task02_iron_shirt.cpp
 /usr/include/stdc-predef.h
 /home/<USER>/workspace/arm_control_ws/src/robot_controller/include/robot_controller/Def_Class.h
 /home/<USER>/workspace/arm_control_ws/src/robot_controller/include/robot_controller/Def_Struct.h
 /usr/include/c++/11/vector
 /usr/include/c++/11/bits/stl_algobase.h
 /usr/include/x86_64-linux-gnu/c++/11/bits/c++config.h
 /usr/include/x86_64-linux-gnu/c++/11/bits/os_defines.h
 /usr/include/features.h
 /usr/include/x86_64-linux-gnu/sys/cdefs.h
 /usr/include/x86_64-linux-gnu/bits/wordsize.h
 /usr/include/x86_64-linux-gnu/bits/long-double.h
 /usr/include/x86_64-linux-gnu/gnu/stubs.h
 /usr/include/x86_64-linux-gnu/gnu/stubs-64.h
 /usr/include/x86_64-linux-gnu/c++/11/bits/cpu_defines.h
 /usr/include/c++/11/pstl/pstl_config.h
 /usr/include/c++/11/bits/functexcept.h
 /usr/include/c++/11/bits/exception_defines.h
 /usr/include/c++/11/bits/cpp_type_traits.h
 /usr/include/c++/11/ext/type_traits.h
 /usr/include/c++/11/ext/numeric_traits.h
 /usr/include/c++/11/bits/stl_pair.h
 /usr/include/c++/11/bits/move.h
 /usr/include/c++/11/type_traits
 /usr/include/c++/11/bits/stl_iterator_base_types.h
 /usr/include/c++/11/bits/stl_iterator_base_funcs.h
 /usr/include/c++/11/bits/concept_check.h
 /usr/include/c++/11/debug/assertions.h
 /usr/include/c++/11/bits/stl_iterator.h
 /usr/include/c++/11/bits/ptr_traits.h
 /usr/include/c++/11/debug/debug.h
 /usr/include/c++/11/bits/predefined_ops.h
 /usr/include/c++/11/bits/allocator.h
 /usr/include/x86_64-linux-gnu/c++/11/bits/c++allocator.h
 /usr/include/c++/11/ext/new_allocator.h
 /usr/include/c++/11/new
 /usr/include/c++/11/bits/exception.h
 /usr/include/c++/11/bits/memoryfwd.h
 /usr/include/c++/11/bits/stl_construct.h
 /usr/include/c++/11/bits/stl_uninitialized.h
 /usr/include/c++/11/ext/alloc_traits.h
 /usr/include/c++/11/bits/alloc_traits.h
 /usr/include/c++/11/bits/stl_vector.h
 /usr/include/c++/11/initializer_list
 /usr/include/c++/11/bits/stl_bvector.h
 /usr/include/c++/11/bits/functional_hash.h
 /usr/include/c++/11/bits/hash_bytes.h
 /usr/include/c++/11/bits/range_access.h
 /usr/include/c++/11/bits/vector.tcc
 /usr/include/eigen3/Eigen/Core
 /usr/include/eigen3/Eigen/src/Core/util/DisableStupidWarnings.h
 /usr/include/eigen3/Eigen/src/Core/util/Macros.h
 /usr/include/c++/11/complex
 /usr/include/c++/11/cmath
 /usr/include/math.h
 /usr/include/x86_64-linux-gnu/bits/libc-header-start.h
 /usr/include/x86_64-linux-gnu/bits/types.h
 /usr/include/x86_64-linux-gnu/bits/timesize.h
 /usr/include/x86_64-linux-gnu/bits/typesizes.h
 /usr/include/x86_64-linux-gnu/bits/time64.h
 /usr/include/x86_64-linux-gnu/bits/math-vector.h
 /usr/include/x86_64-linux-gnu/bits/libm-simd-decl-stubs.h
 /usr/include/x86_64-linux-gnu/bits/floatn.h
 /usr/include/x86_64-linux-gnu/bits/floatn-common.h
 /usr/include/x86_64-linux-gnu/bits/flt-eval-method.h
 /usr/include/x86_64-linux-gnu/bits/fp-logb.h
 /usr/include/x86_64-linux-gnu/bits/fp-fast.h
 /usr/include/x86_64-linux-gnu/bits/mathcalls-helper-functions.h
 /usr/include/x86_64-linux-gnu/bits/mathcalls.h
 /usr/include/x86_64-linux-gnu/bits/mathcalls-narrow.h
 /usr/include/x86_64-linux-gnu/bits/iscanonical.h
 /usr/include/c++/11/bits/std_abs.h
 /usr/include/stdlib.h
 /usr/lib/gcc/x86_64-linux-gnu/11/include/stddef.h
 /usr/include/x86_64-linux-gnu/bits/waitflags.h
 /usr/include/x86_64-linux-gnu/bits/waitstatus.h
 /usr/include/x86_64-linux-gnu/bits/types/locale_t.h
 /usr/include/x86_64-linux-gnu/bits/types/__locale_t.h
 /usr/include/x86_64-linux-gnu/sys/types.h
 /usr/include/x86_64-linux-gnu/bits/types/clock_t.h
 /usr/include/x86_64-linux-gnu/bits/types/clockid_t.h
 /usr/include/x86_64-linux-gnu/bits/types/time_t.h
 /usr/include/x86_64-linux-gnu/bits/types/timer_t.h
 /usr/include/x86_64-linux-gnu/bits/stdint-intn.h
 /usr/include/endian.h
 /usr/include/x86_64-linux-gnu/bits/endian.h
 /usr/include/x86_64-linux-gnu/bits/endianness.h
 /usr/include/x86_64-linux-gnu/bits/byteswap.h
 /usr/include/x86_64-linux-gnu/bits/uintn-identity.h
 /usr/include/x86_64-linux-gnu/sys/select.h
 /usr/include/x86_64-linux-gnu/bits/select.h
 /usr/include/x86_64-linux-gnu/bits/types/sigset_t.h
 /usr/include/x86_64-linux-gnu/bits/types/__sigset_t.h
 /usr/include/x86_64-linux-gnu/bits/types/struct_timeval.h
 /usr/include/x86_64-linux-gnu/bits/types/struct_timespec.h
 /usr/include/x86_64-linux-gnu/bits/pthreadtypes.h
 /usr/include/x86_64-linux-gnu/bits/thread-shared-types.h
 /usr/include/x86_64-linux-gnu/bits/pthreadtypes-arch.h
 /usr/include/x86_64-linux-gnu/bits/struct_mutex.h
 /usr/include/x86_64-linux-gnu/bits/struct_rwlock.h
 /usr/include/alloca.h
 /usr/include/x86_64-linux-gnu/bits/stdlib-float.h
 /usr/include/c++/11/bits/specfun.h
 /usr/include/c++/11/limits
 /usr/include/c++/11/tr1/gamma.tcc
 /usr/include/c++/11/tr1/special_function_util.h
 /usr/include/c++/11/tr1/bessel_function.tcc
 /usr/include/c++/11/tr1/beta_function.tcc
 /usr/include/c++/11/tr1/ell_integral.tcc
 /usr/include/c++/11/tr1/exp_integral.tcc
 /usr/include/c++/11/tr1/hypergeometric.tcc
 /usr/include/c++/11/tr1/legendre_function.tcc
 /usr/include/c++/11/tr1/modified_bessel_func.tcc
 /usr/include/c++/11/tr1/poly_hermite.tcc
 /usr/include/c++/11/tr1/poly_laguerre.tcc
 /usr/include/c++/11/tr1/riemann_zeta.tcc
 /usr/include/c++/11/sstream
 /usr/include/c++/11/istream
 /usr/include/c++/11/ios
 /usr/include/c++/11/iosfwd
 /usr/include/c++/11/bits/stringfwd.h
 /usr/include/c++/11/bits/postypes.h
 /usr/include/c++/11/cwchar
 /usr/include/wchar.h
 /usr/lib/gcc/x86_64-linux-gnu/11/include/stdarg.h
 /usr/include/x86_64-linux-gnu/bits/wchar.h
 /usr/include/x86_64-linux-gnu/bits/types/wint_t.h
 /usr/include/x86_64-linux-gnu/bits/types/mbstate_t.h
 /usr/include/x86_64-linux-gnu/bits/types/__mbstate_t.h
 /usr/include/x86_64-linux-gnu/bits/types/__FILE.h
 /usr/include/x86_64-linux-gnu/bits/types/FILE.h
 /usr/include/c++/11/exception
 /usr/include/c++/11/bits/exception_ptr.h
 /usr/include/c++/11/bits/cxxabi_init_exception.h
 /usr/include/c++/11/typeinfo
 /usr/include/c++/11/bits/nested_exception.h
 /usr/include/c++/11/bits/char_traits.h
 /usr/include/c++/11/cstdint
 /usr/lib/gcc/x86_64-linux-gnu/11/include/stdint.h
 /usr/include/stdint.h
 /usr/include/x86_64-linux-gnu/bits/stdint-uintn.h
 /usr/include/c++/11/bits/localefwd.h
 /usr/include/x86_64-linux-gnu/c++/11/bits/c++locale.h
 /usr/include/c++/11/clocale
 /usr/include/locale.h
 /usr/include/x86_64-linux-gnu/bits/locale.h
 /usr/include/c++/11/cctype
 /usr/include/ctype.h
 /usr/include/c++/11/bits/ios_base.h
 /usr/include/c++/11/ext/atomicity.h
 /usr/include/x86_64-linux-gnu/c++/11/bits/gthr.h
 /usr/include/x86_64-linux-gnu/c++/11/bits/gthr-default.h
 /usr/include/pthread.h
 /usr/include/sched.h
 /usr/include/x86_64-linux-gnu/bits/sched.h
 /usr/include/x86_64-linux-gnu/bits/types/struct_sched_param.h
 /usr/include/x86_64-linux-gnu/bits/cpu-set.h
 /usr/include/time.h
 /usr/include/x86_64-linux-gnu/bits/time.h
 /usr/include/x86_64-linux-gnu/bits/timex.h
 /usr/include/x86_64-linux-gnu/bits/types/struct_tm.h
 /usr/include/x86_64-linux-gnu/bits/types/struct_itimerspec.h
 /usr/include/x86_64-linux-gnu/bits/setjmp.h
 /usr/include/x86_64-linux-gnu/c++/11/bits/atomic_word.h
 /usr/include/c++/11/bits/locale_classes.h
 /usr/include/c++/11/string
 /usr/include/c++/11/bits/ostream_insert.h
 /usr/include/c++/11/bits/cxxabi_forced.h
 /usr/include/c++/11/bits/stl_function.h
 /usr/include/c++/11/backward/binders.h
 /usr/include/c++/11/bits/basic_string.h
 /usr/include/c++/11/string_view
 /usr/include/c++/11/bits/string_view.tcc
 /usr/include/c++/11/ext/string_conversions.h
 /usr/include/c++/11/cstdlib
 /usr/include/c++/11/cstdio
 /usr/include/stdio.h
 /usr/include/x86_64-linux-gnu/bits/types/__fpos_t.h
 /usr/include/x86_64-linux-gnu/bits/types/__fpos64_t.h
 /usr/include/x86_64-linux-gnu/bits/types/struct_FILE.h
 /usr/include/x86_64-linux-gnu/bits/types/cookie_io_functions_t.h
 /usr/include/x86_64-linux-gnu/bits/stdio_lim.h
 /usr/include/x86_64-linux-gnu/bits/sys_errlist.h
 /usr/include/c++/11/cerrno
 /usr/include/errno.h
 /usr/include/x86_64-linux-gnu/bits/errno.h
 /usr/include/linux/errno.h
 /usr/include/x86_64-linux-gnu/asm/errno.h
 /usr/include/asm-generic/errno.h
 /usr/include/asm-generic/errno-base.h
 /usr/include/x86_64-linux-gnu/bits/types/error_t.h
 /usr/include/c++/11/bits/charconv.h
 /usr/include/c++/11/bits/basic_string.tcc
 /usr/include/c++/11/bits/locale_classes.tcc
 /usr/include/c++/11/system_error
 /usr/include/x86_64-linux-gnu/c++/11/bits/error_constants.h
 /usr/include/c++/11/stdexcept
 /usr/include/c++/11/streambuf
 /usr/include/c++/11/bits/streambuf.tcc
 /usr/include/c++/11/bits/basic_ios.h
 /usr/include/c++/11/bits/locale_facets.h
 /usr/include/c++/11/cwctype
 /usr/include/wctype.h
 /usr/include/x86_64-linux-gnu/bits/wctype-wchar.h
 /usr/include/x86_64-linux-gnu/c++/11/bits/ctype_base.h
 /usr/include/c++/11/bits/streambuf_iterator.h
 /usr/include/x86_64-linux-gnu/c++/11/bits/ctype_inline.h
 /usr/include/c++/11/bits/locale_facets.tcc
 /usr/include/c++/11/bits/basic_ios.tcc
 /usr/include/c++/11/ostream
 /usr/include/c++/11/bits/ostream.tcc
 /usr/include/c++/11/bits/istream.tcc
 /usr/include/c++/11/bits/sstream.tcc
 /usr/include/eigen3/Eigen/src/Core/util/MKL_support.h
 /usr/lib/gcc/x86_64-linux-gnu/11/include/mmintrin.h
 /usr/lib/gcc/x86_64-linux-gnu/11/include/emmintrin.h
 /usr/lib/gcc/x86_64-linux-gnu/11/include/xmmintrin.h
 /usr/lib/gcc/x86_64-linux-gnu/11/include/mm_malloc.h
 /usr/include/c++/11/stdlib.h
 /usr/include/c++/11/cstddef
 /usr/include/c++/11/cassert
 /usr/include/assert.h
 /usr/include/c++/11/functional
 /usr/include/c++/11/tuple
 /usr/include/c++/11/utility
 /usr/include/c++/11/bits/stl_relops.h
 /usr/include/c++/11/array
 /usr/include/c++/11/bits/uses_allocator.h
 /usr/include/c++/11/bits/invoke.h
 /usr/include/c++/11/bits/refwrap.h
 /usr/include/c++/11/bits/std_function.h
 /usr/include/c++/11/unordered_map
 /usr/include/c++/11/ext/aligned_buffer.h
 /usr/include/c++/11/bits/hashtable.h
 /usr/include/c++/11/bits/hashtable_policy.h
 /usr/include/c++/11/bits/enable_special_members.h
 /usr/include/c++/11/bits/node_handle.h
 /usr/include/c++/11/bits/unordered_map.h
 /usr/include/c++/11/bits/erase_if.h
 /usr/include/c++/11/bits/stl_algo.h
 /usr/include/c++/11/bits/algorithmfwd.h
 /usr/include/c++/11/bits/stl_heap.h
 /usr/include/c++/11/bits/stl_tempbuf.h
 /usr/include/c++/11/bits/uniform_int_dist.h
 /usr/include/c++/11/cstring
 /usr/include/string.h
 /usr/include/strings.h
 /usr/include/c++/11/climits
 /usr/lib/gcc/x86_64-linux-gnu/11/include/limits.h
 /usr/lib/gcc/x86_64-linux-gnu/11/include/syslimits.h
 /usr/include/limits.h
 /usr/include/x86_64-linux-gnu/bits/posix1_lim.h
 /usr/include/x86_64-linux-gnu/bits/local_lim.h
 /usr/include/linux/limits.h
 /usr/include/x86_64-linux-gnu/bits/posix2_lim.h
 /usr/include/x86_64-linux-gnu/bits/xopen_lim.h
 /usr/include/x86_64-linux-gnu/bits/uio_lim.h
 /usr/include/c++/11/algorithm
 /usr/include/c++/11/pstl/glue_algorithm_defs.h
 /usr/include/c++/11/pstl/execution_defs.h
 /usr/include/eigen3/Eigen/src/Core/util/Constants.h
 /usr/include/eigen3/Eigen/src/Core/util/Meta.h
 /usr/include/eigen3/Eigen/src/Core/util/ForwardDeclarations.h
 /usr/include/eigen3/Eigen/src/Core/util/StaticAssert.h
 /usr/include/eigen3/Eigen/src/Core/util/XprHelper.h
 /usr/include/eigen3/Eigen/src/Core/util/Memory.h
 /usr/include/eigen3/Eigen/src/Core/NumTraits.h
 /usr/include/eigen3/Eigen/src/Core/MathFunctions.h
 /usr/include/eigen3/Eigen/src/Core/GenericPacketMath.h
 /usr/include/eigen3/Eigen/src/Core/MathFunctionsImpl.h
 /usr/include/eigen3/Eigen/src/Core/arch/Default/ConjHelper.h
 /usr/include/eigen3/Eigen/src/Core/arch/SSE/PacketMath.h
 /usr/include/eigen3/Eigen/src/Core/arch/SSE/MathFunctions.h
 /usr/include/eigen3/Eigen/src/Core/arch/SSE/Complex.h
 /usr/include/eigen3/Eigen/src/Core/arch/SSE/TypeCasting.h
 /usr/include/eigen3/Eigen/src/Core/arch/CUDA/Half.h
 /usr/include/eigen3/Eigen/src/Core/arch/CUDA/PacketMathHalf.h
 /usr/include/eigen3/Eigen/src/Core/arch/CUDA/TypeCasting.h
 /usr/include/eigen3/Eigen/src/Core/arch/Default/Settings.h
 /usr/include/eigen3/Eigen/src/Core/functors/TernaryFunctors.h
 /usr/include/eigen3/Eigen/src/Core/functors/BinaryFunctors.h
 /usr/include/eigen3/Eigen/src/Core/functors/UnaryFunctors.h
 /usr/include/eigen3/Eigen/src/Core/functors/NullaryFunctors.h
 /usr/include/eigen3/Eigen/src/Core/functors/StlFunctors.h
 /usr/include/eigen3/Eigen/src/Core/functors/AssignmentFunctors.h
 /usr/include/eigen3/Eigen/src/Core/arch/CUDA/Complex.h
 /usr/include/eigen3/Eigen/src/Core/IO.h
 /usr/include/eigen3/Eigen/src/Core/DenseCoeffsBase.h
 /usr/include/eigen3/Eigen/src/Core/DenseBase.h
 /usr/include/eigen3/Eigen/src/plugins/BlockMethods.h
 /usr/include/eigen3/Eigen/src/Core/MatrixBase.h
 /usr/include/eigen3/Eigen/src/plugins/CommonCwiseUnaryOps.h
 /usr/include/eigen3/Eigen/src/plugins/CommonCwiseBinaryOps.h
 /usr/include/eigen3/Eigen/src/plugins/MatrixCwiseUnaryOps.h
 /usr/include/eigen3/Eigen/src/plugins/MatrixCwiseBinaryOps.h
 /usr/include/eigen3/Eigen/src/Core/EigenBase.h
 /usr/include/eigen3/Eigen/src/Core/Product.h
 /usr/include/eigen3/Eigen/src/Core/CoreEvaluators.h
 /usr/include/eigen3/Eigen/src/Core/AssignEvaluator.h
 /usr/include/eigen3/Eigen/src/Core/Assign.h
 /usr/include/eigen3/Eigen/src/Core/ArrayBase.h
 /usr/include/eigen3/Eigen/src/plugins/ArrayCwiseUnaryOps.h
 /usr/include/eigen3/Eigen/src/plugins/ArrayCwiseBinaryOps.h
 /usr/include/eigen3/Eigen/src/Core/util/BlasUtil.h
 /usr/include/eigen3/Eigen/src/Core/DenseStorage.h
 /usr/include/eigen3/Eigen/src/Core/NestByValue.h
 /usr/include/eigen3/Eigen/src/Core/ReturnByValue.h
 /usr/include/eigen3/Eigen/src/Core/NoAlias.h
 /usr/include/eigen3/Eigen/src/Core/PlainObjectBase.h
 /usr/include/eigen3/Eigen/src/Core/Matrix.h
 /usr/include/eigen3/Eigen/src/Core/Array.h
 /usr/include/eigen3/Eigen/src/Core/CwiseTernaryOp.h
 /usr/include/eigen3/Eigen/src/Core/CwiseBinaryOp.h
 /usr/include/eigen3/Eigen/src/Core/CwiseUnaryOp.h
 /usr/include/eigen3/Eigen/src/Core/CwiseNullaryOp.h
 /usr/include/eigen3/Eigen/src/Core/CwiseUnaryView.h
 /usr/include/eigen3/Eigen/src/Core/SelfCwiseBinaryOp.h
 /usr/include/eigen3/Eigen/src/Core/Dot.h
 /usr/include/eigen3/Eigen/src/Core/StableNorm.h
 /usr/include/eigen3/Eigen/src/Core/Stride.h
 /usr/include/eigen3/Eigen/src/Core/MapBase.h
 /usr/include/eigen3/Eigen/src/Core/Map.h
 /usr/include/eigen3/Eigen/src/Core/Ref.h
 /usr/include/eigen3/Eigen/src/Core/Block.h
 /usr/include/eigen3/Eigen/src/Core/VectorBlock.h
 /usr/include/eigen3/Eigen/src/Core/Transpose.h
 /usr/include/eigen3/Eigen/src/Core/DiagonalMatrix.h
 /usr/include/eigen3/Eigen/src/Core/Diagonal.h
 /usr/include/eigen3/Eigen/src/Core/DiagonalProduct.h
 /usr/include/eigen3/Eigen/src/Core/Redux.h
 /usr/include/eigen3/Eigen/src/Core/Visitor.h
 /usr/include/eigen3/Eigen/src/Core/Fuzzy.h
 /usr/include/eigen3/Eigen/src/Core/Swap.h
 /usr/include/eigen3/Eigen/src/Core/CommaInitializer.h
 /usr/include/eigen3/Eigen/src/Core/GeneralProduct.h
 /usr/include/eigen3/Eigen/src/Core/Solve.h
 /usr/include/eigen3/Eigen/src/Core/Inverse.h
 /usr/include/eigen3/Eigen/src/Core/SolverBase.h
 /usr/include/eigen3/Eigen/src/Core/PermutationMatrix.h
 /usr/include/eigen3/Eigen/src/Core/Transpositions.h
 /usr/include/eigen3/Eigen/src/Core/TriangularMatrix.h
 /usr/include/eigen3/Eigen/src/Core/SelfAdjointView.h
 /usr/include/eigen3/Eigen/src/Core/products/GeneralBlockPanelKernel.h
 /usr/include/eigen3/Eigen/src/Core/products/Parallelizer.h
 /usr/include/eigen3/Eigen/src/Core/ProductEvaluators.h
 /usr/include/eigen3/Eigen/src/Core/products/GeneralMatrixVector.h
 /usr/include/eigen3/Eigen/src/Core/products/GeneralMatrixMatrix.h
 /usr/include/eigen3/Eigen/src/Core/SolveTriangular.h
 /usr/include/eigen3/Eigen/src/Core/products/GeneralMatrixMatrixTriangular.h
 /usr/include/eigen3/Eigen/src/Core/products/SelfadjointMatrixVector.h
 /usr/include/eigen3/Eigen/src/Core/products/SelfadjointMatrixMatrix.h
 /usr/include/eigen3/Eigen/src/Core/products/SelfadjointProduct.h
 /usr/include/eigen3/Eigen/src/Core/products/SelfadjointRank2Update.h
 /usr/include/eigen3/Eigen/src/Core/products/TriangularMatrixVector.h
 /usr/include/eigen3/Eigen/src/Core/products/TriangularMatrixMatrix.h
 /usr/include/eigen3/Eigen/src/Core/products/TriangularSolverMatrix.h
 /usr/include/eigen3/Eigen/src/Core/products/TriangularSolverVector.h
 /usr/include/eigen3/Eigen/src/Core/BandMatrix.h
 /usr/include/eigen3/Eigen/src/Core/CoreIterators.h
 /usr/include/eigen3/Eigen/src/Core/ConditionEstimator.h
 /usr/include/eigen3/Eigen/src/Core/BooleanRedux.h
 /usr/include/eigen3/Eigen/src/Core/Select.h
 /usr/include/eigen3/Eigen/src/Core/VectorwiseOp.h
 /usr/include/eigen3/Eigen/src/Core/Random.h
 /usr/include/eigen3/Eigen/src/Core/Replicate.h
 /usr/include/eigen3/Eigen/src/Core/Reverse.h
 /usr/include/eigen3/Eigen/src/Core/ArrayWrapper.h
 /usr/include/eigen3/Eigen/src/Core/GlobalFunctions.h
 /usr/include/eigen3/Eigen/src/Core/util/ReenableStupidWarnings.h
 /usr/include/eigen3/Eigen/Dense
 /usr/include/eigen3/Eigen/Core
 /usr/include/eigen3/Eigen/LU
 /usr/include/eigen3/Eigen/src/misc/Kernel.h
 /usr/include/eigen3/Eigen/src/misc/Image.h
 /usr/include/eigen3/Eigen/src/LU/FullPivLU.h
 /usr/include/eigen3/Eigen/src/LU/PartialPivLU.h
 /usr/include/eigen3/Eigen/src/LU/Determinant.h
 /usr/include/eigen3/Eigen/src/LU/InverseImpl.h
 /usr/include/eigen3/Eigen/src/LU/arch/Inverse_SSE.h
 /usr/include/eigen3/Eigen/Cholesky
 /usr/include/eigen3/Eigen/Jacobi
 /usr/include/eigen3/Eigen/src/Jacobi/Jacobi.h
 /usr/include/eigen3/Eigen/src/Cholesky/LLT.h
 /usr/include/eigen3/Eigen/src/Cholesky/LDLT.h
 /usr/include/eigen3/Eigen/QR
 /usr/include/eigen3/Eigen/Householder
 /usr/include/eigen3/Eigen/src/Householder/Householder.h
 /usr/include/eigen3/Eigen/src/Householder/HouseholderSequence.h
 /usr/include/eigen3/Eigen/src/Householder/BlockHouseholder.h
 /usr/include/eigen3/Eigen/src/QR/HouseholderQR.h
 /usr/include/eigen3/Eigen/src/QR/FullPivHouseholderQR.h
 /usr/include/eigen3/Eigen/src/QR/ColPivHouseholderQR.h
 /usr/include/eigen3/Eigen/src/QR/CompleteOrthogonalDecomposition.h
 /usr/include/eigen3/Eigen/SVD
 /usr/include/eigen3/Eigen/src/misc/RealSvd2x2.h
 /usr/include/eigen3/Eigen/src/SVD/UpperBidiagonalization.h
 /usr/include/eigen3/Eigen/src/SVD/SVDBase.h
 /usr/include/eigen3/Eigen/src/SVD/JacobiSVD.h
 /usr/include/eigen3/Eigen/src/SVD/BDCSVD.h
 /usr/include/eigen3/Eigen/Geometry
 /usr/include/eigen3/Eigen/src/Geometry/OrthoMethods.h
 /usr/include/eigen3/Eigen/src/Geometry/EulerAngles.h
 /usr/include/eigen3/Eigen/src/Geometry/Homogeneous.h
 /usr/include/eigen3/Eigen/src/Geometry/RotationBase.h
 /usr/include/eigen3/Eigen/src/Geometry/Rotation2D.h
 /usr/include/eigen3/Eigen/src/Geometry/Quaternion.h
 /usr/include/eigen3/Eigen/src/Geometry/AngleAxis.h
 /usr/include/eigen3/Eigen/src/Geometry/Transform.h
 /usr/include/eigen3/Eigen/src/Geometry/Translation.h
 /usr/include/eigen3/Eigen/src/Geometry/Scaling.h
 /usr/include/eigen3/Eigen/src/Geometry/Hyperplane.h
 /usr/include/eigen3/Eigen/src/Geometry/ParametrizedLine.h
 /usr/include/eigen3/Eigen/src/Geometry/AlignedBox.h
 /usr/include/eigen3/Eigen/src/Geometry/Umeyama.h
 /usr/include/eigen3/Eigen/src/Geometry/arch/Geometry_SSE.h
 /usr/include/eigen3/Eigen/Eigenvalues
 /usr/include/eigen3/Eigen/src/Eigenvalues/Tridiagonalization.h
 /usr/include/eigen3/Eigen/src/Eigenvalues/RealSchur.h
 /usr/include/eigen3/Eigen/src/Eigenvalues/HessenbergDecomposition.h
 /usr/include/eigen3/Eigen/src/Eigenvalues/EigenSolver.h
 /usr/include/eigen3/Eigen/src/Eigenvalues/RealSchur.h
 /usr/include/eigen3/Eigen/src/Eigenvalues/SelfAdjointEigenSolver.h
 /usr/include/eigen3/Eigen/src/Eigenvalues/Tridiagonalization.h
 /usr/include/eigen3/Eigen/src/Eigenvalues/GeneralizedSelfAdjointEigenSolver.h
 /usr/include/eigen3/Eigen/src/Eigenvalues/HessenbergDecomposition.h
 /usr/include/eigen3/Eigen/src/Eigenvalues/ComplexSchur.h
 /usr/include/eigen3/Eigen/src/Eigenvalues/ComplexEigenSolver.h
 /usr/include/eigen3/Eigen/src/Eigenvalues/ComplexSchur.h
 /usr/include/eigen3/Eigen/src/Eigenvalues/RealQZ.h
 /usr/include/eigen3/Eigen/src/Eigenvalues/GeneralizedEigenSolver.h
 /usr/include/eigen3/Eigen/src/Eigenvalues/RealQZ.h
 /usr/include/eigen3/Eigen/src/Eigenvalues/MatrixBaseEigenvalues.h
 /usr/include/eigen3/Eigen/LU
 /opt/ros/noetic/include/visualization_msgs/Marker.h
 /usr/include/c++/11/memory
 /usr/include/c++/11/bits/stl_raw_storage_iter.h
 /usr/include/c++/11/bits/align.h
 /usr/include/c++/11/bit
 /usr/include/c++/11/bits/unique_ptr.h
 /usr/include/c++/11/bits/shared_ptr.h
 /usr/include/c++/11/bits/shared_ptr_base.h
 /usr/include/c++/11/bits/allocated_ptr.h
 /usr/include/c++/11/ext/concurrence.h
 /usr/include/c++/11/bits/shared_ptr_atomic.h
 /usr/include/c++/11/bits/atomic_base.h
 /usr/include/c++/11/bits/atomic_lockfree_defines.h
 /usr/include/c++/11/backward/auto_ptr.h
 /usr/include/c++/11/pstl/glue_memory_defs.h
 /opt/ros/noetic/include/ros/types.h
 /opt/ros/noetic/include/ros/serialization.h
 /opt/ros/noetic/include/ros/roscpp_serialization_macros.h
 /opt/ros/noetic/include/ros/macros.h
 /opt/ros/noetic/include/ros/time.h
 /opt/ros/noetic/include/ros/platform.h
 /usr/include/c++/11/iostream
 /opt/ros/noetic/include/ros/exception.h
 /opt/ros/noetic/include/ros/duration.h
 /usr/include/c++/11/math.h
 /opt/ros/noetic/include/ros/rostime_decl.h
 /home/<USER>/miniconda3/envs/coal/include/boost/math/special_functions/round.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/math/tools/config.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/math/tools/is_standalone.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/config.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/config/user.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/config/detail/select_compiler_config.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/config/compiler/gcc.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/config/detail/select_stdlib_config.hpp
 /usr/include/c++/11/version
 /home/<USER>/miniconda3/envs/coal/include/boost/config/stdlib/libstdcpp3.hpp
 /usr/include/unistd.h
 /usr/include/x86_64-linux-gnu/bits/posix_opt.h
 /usr/include/x86_64-linux-gnu/bits/environments.h
 /usr/include/x86_64-linux-gnu/bits/confname.h
 /usr/include/x86_64-linux-gnu/bits/getopt_posix.h
 /usr/include/x86_64-linux-gnu/bits/getopt_core.h
 /usr/include/x86_64-linux-gnu/bits/unistd_ext.h
 /home/<USER>/miniconda3/envs/coal/include/boost/config/detail/select_platform_config.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/config/platform/linux.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/config/detail/posix_features.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/config/detail/suffix.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/config/helper_macros.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/config/detail/cxx_composite.hpp
 /usr/include/c++/11/cfloat
 /usr/lib/gcc/x86_64-linux-gnu/11/include/float.h
 /home/<USER>/miniconda3/envs/coal/include/boost/math/tools/user.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/math/ccmath/detail/config.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/math/tools/is_constant_evaluated.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/math/policies/error_handling.hpp
 /usr/include/c++/11/iomanip
 /usr/include/c++/11/locale
 /usr/include/c++/11/bits/locale_facets_nonio.h
 /usr/include/c++/11/ctime
 /usr/include/x86_64-linux-gnu/c++/11/bits/time_members.h
 /usr/include/x86_64-linux-gnu/c++/11/bits/messages_members.h
 /usr/include/libintl.h
 /usr/include/c++/11/bits/codecvt.h
 /usr/include/c++/11/bits/locale_facets_nonio.tcc
 /usr/include/c++/11/bits/locale_conv.h
 /usr/include/c++/11/bits/quoted_string.h
 /home/<USER>/miniconda3/envs/coal/include/boost/math/policies/policy.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/math/tools/mp.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/math/tools/precision.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/math/tools/assert.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/assert.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/static_assert.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/detail/workaround.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/config/workaround.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/math/tools/throw_exception.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/throw_exception.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/exception/exception.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/assert/source_location.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/cstdint.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/math/special_functions/math_fwd.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/math/special_functions/detail/round_fwd.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/math/tools/promotion.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/math/special_functions/fpclassify.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/math/tools/real_cast.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/math/special_functions/detail/fp_traits.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/predef/other/endian.h
 /home/<USER>/miniconda3/envs/coal/include/boost/predef/version_number.h
 /home/<USER>/miniconda3/envs/coal/include/boost/predef/make.h
 /home/<USER>/miniconda3/envs/coal/include/boost/predef/detail/test.h
 /home/<USER>/miniconda3/envs/coal/include/boost/predef/library/c/gnu.h
 /home/<USER>/miniconda3/envs/coal/include/boost/predef/library/c/_prefix.h
 /home/<USER>/miniconda3/envs/coal/include/boost/predef/detail/_cassert.h
 /home/<USER>/miniconda3/envs/coal/include/boost/predef/os/macos.h
 /home/<USER>/miniconda3/envs/coal/include/boost/predef/os/ios.h
 /home/<USER>/miniconda3/envs/coal/include/boost/predef/os/bsd.h
 /home/<USER>/miniconda3/envs/coal/include/boost/predef/os/bsd/bsdi.h
 /home/<USER>/miniconda3/envs/coal/include/boost/predef/os/bsd/dragonfly.h
 /home/<USER>/miniconda3/envs/coal/include/boost/predef/os/bsd/free.h
 /home/<USER>/miniconda3/envs/coal/include/boost/predef/os/bsd/open.h
 /home/<USER>/miniconda3/envs/coal/include/boost/predef/os/bsd/net.h
 /home/<USER>/miniconda3/envs/coal/include/boost/predef/platform/android.h
 /usr/lib/gcc/x86_64-linux-gnu/11/include/quadmath.h
 /home/<USER>/miniconda3/envs/coal/include/boost/math/ccmath/ldexp.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/math/ccmath/abs.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/math/ccmath/isnan.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/math/ccmath/isinf.hpp
 /usr/include/x86_64-linux-gnu/sys/time.h
 /opt/ros/noetic/include/ros/serialized_message.h
 /home/<USER>/miniconda3/envs/coal/include/boost/shared_array.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/smart_ptr/shared_array.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/smart_ptr/detail/requires_cxx11.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/config/pragma_message.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/core/checked_delete.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/smart_ptr/shared_ptr.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/smart_ptr/detail/shared_count.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/smart_ptr/bad_weak_ptr.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/smart_ptr/detail/sp_counted_base.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/smart_ptr/detail/sp_has_gcc_intrinsics.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/smart_ptr/detail/sp_has_sync_intrinsics.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/smart_ptr/detail/sp_counted_base_gcc_atomic.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/smart_ptr/detail/sp_typeinfo_.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/smart_ptr/detail/sp_counted_impl.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/smart_ptr/detail/sp_noexcept.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/core/addressof.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/smart_ptr/detail/sp_disable_deprecated.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/smart_ptr/detail/sp_convertible.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/smart_ptr/detail/sp_nullptr_t.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/smart_ptr/detail/spinlock_pool.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/smart_ptr/detail/spinlock.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/smart_ptr/detail/spinlock_gcc_atomic.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/smart_ptr/detail/yield_k.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/core/yield_primitives.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/core/detail/sp_thread_pause.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/core/detail/sp_thread_yield.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/core/detail/sp_thread_sleep.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/smart_ptr/detail/operator_bool.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/smart_ptr/detail/local_sp_deleter.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/smart_ptr/detail/local_counted_base.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/shared_ptr.hpp
 /opt/ros/noetic/include/ros/message_traits.h
 /opt/ros/noetic/include/ros/message_forward.h
 /home/<USER>/miniconda3/envs/coal/include/boost/utility/enable_if.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/core/enable_if.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/type_traits/remove_const.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/type_traits/remove_reference.hpp
 /opt/ros/noetic/include/ros/builtin_message_traits.h
 /opt/ros/noetic/include/ros/message_traits.h
 /opt/ros/noetic/include/ros/datatypes.h
 /usr/include/c++/11/map
 /usr/include/c++/11/bits/stl_tree.h
 /usr/include/c++/11/bits/stl_map.h
 /usr/include/c++/11/bits/stl_multimap.h
 /usr/include/c++/11/set
 /usr/include/c++/11/bits/stl_set.h
 /usr/include/c++/11/bits/stl_multiset.h
 /usr/include/c++/11/list
 /usr/include/c++/11/bits/stl_list.h
 /usr/include/c++/11/bits/list.tcc
 /home/<USER>/miniconda3/envs/coal/include/boost/array.hpp
 /usr/include/c++/11/iterator
 /usr/include/c++/11/bits/stream_iterator.h
 /home/<USER>/miniconda3/envs/coal/include/boost/core/invoke_swap.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/call_traits.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/detail/call_traits.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/type_traits/is_arithmetic.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/type_traits/is_integral.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/type_traits/integral_constant.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/type_traits/is_floating_point.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/type_traits/is_enum.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/type_traits/intrinsics.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/type_traits/detail/config.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/version.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/type_traits/is_pointer.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/mpl/and.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/mpl/aux_/config/use_preprocessed.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/mpl/bool.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/mpl/bool_fwd.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/mpl/aux_/adl_barrier.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/mpl/aux_/config/adl.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/mpl/aux_/config/msvc.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/mpl/aux_/config/intel.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/mpl/aux_/config/gcc.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/mpl/aux_/config/workaround.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/mpl/integral_c_tag.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/mpl/aux_/config/static_constant.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/mpl/aux_/nested_type_wknd.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/mpl/aux_/na_spec.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/mpl/lambda_fwd.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/mpl/void_fwd.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/mpl/aux_/na.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/mpl/aux_/na_fwd.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/mpl/aux_/config/ctps.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/mpl/aux_/config/lambda.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/mpl/aux_/config/ttp.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/mpl/int.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/mpl/int_fwd.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/mpl/aux_/nttp_decl.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/mpl/aux_/config/nttp.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/mpl/aux_/integral_wrapper.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/mpl/aux_/static_cast.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/preprocessor/cat.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/preprocessor/config/config.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/mpl/aux_/lambda_arity_param.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/mpl/aux_/template_arity_fwd.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/mpl/aux_/arity.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/mpl/aux_/config/dtp.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/mpl/aux_/preprocessor/params.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/mpl/aux_/config/preprocessor.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/preprocessor/comma_if.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/preprocessor/punctuation/comma_if.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/preprocessor/control/if.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/preprocessor/control/iif.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/preprocessor/logical/bool.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/preprocessor/config/limits.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/preprocessor/logical/limits/bool_256.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/preprocessor/facilities/empty.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/preprocessor/punctuation/comma.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/preprocessor/repeat.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/preprocessor/repetition/repeat.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/preprocessor/debug/error.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/preprocessor/detail/auto_rec.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/preprocessor/detail/limits/auto_rec_256.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/preprocessor/tuple/eat.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/preprocessor/repetition/limits/repeat_256.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/preprocessor/inc.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/preprocessor/arithmetic/inc.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/preprocessor/arithmetic/limits/inc_256.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/mpl/aux_/preprocessor/enum.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/mpl/aux_/preprocessor/def_params_tail.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/mpl/limits/arity.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/preprocessor/logical/and.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/preprocessor/logical/bitand.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/preprocessor/identity.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/preprocessor/facilities/identity.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/preprocessor/empty.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/preprocessor/arithmetic/add.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/preprocessor/arithmetic/dec.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/preprocessor/arithmetic/limits/dec_256.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/preprocessor/control/while.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/preprocessor/list/fold_left.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/preprocessor/list/detail/fold_left.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/preprocessor/control/expr_iif.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/preprocessor/list/adt.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/preprocessor/detail/is_binary.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/preprocessor/detail/check.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/preprocessor/logical/compl.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/preprocessor/list/detail/limits/fold_left_256.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/preprocessor/list/limits/fold_left_256.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/preprocessor/list/fold_right.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/preprocessor/list/detail/fold_right.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/preprocessor/list/reverse.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/preprocessor/list/detail/limits/fold_right_256.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/preprocessor/control/detail/while.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/preprocessor/control/detail/limits/while_256.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/preprocessor/control/limits/while_256.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/preprocessor/logical/bitor.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/preprocessor/tuple/elem.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/preprocessor/facilities/expand.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/preprocessor/facilities/overload.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/preprocessor/variadic/size.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/preprocessor/facilities/check_empty.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/preprocessor/variadic/has_opt.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/preprocessor/variadic/limits/size_64.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/preprocessor/tuple/rem.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/preprocessor/tuple/detail/is_single_return.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/preprocessor/variadic/elem.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/preprocessor/variadic/limits/elem_64.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/preprocessor/arithmetic/detail/is_maximum_number.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/preprocessor/comparison/equal.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/preprocessor/comparison/not_equal.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/preprocessor/comparison/limits/not_equal_256.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/preprocessor/arithmetic/detail/maximum_number.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/preprocessor/arithmetic/detail/is_minimum_number.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/preprocessor/logical/not.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/preprocessor/arithmetic/sub.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/mpl/aux_/config/eti.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/mpl/aux_/config/overload_resolution.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/mpl/aux_/lambda_support.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/mpl/aux_/include_preprocessed.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/mpl/aux_/config/compiler.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/preprocessor/stringize.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/mpl/aux_/preprocessed/gcc/and.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/mpl/or.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/mpl/aux_/preprocessed/gcc/or.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/mpl/not.hpp
 /opt/ros/noetic/include/ros/message_operations.h
 /opt/ros/noetic/include/std_msgs/Header.h
 /opt/ros/noetic/include/geometry_msgs/Pose.h
 /opt/ros/noetic/include/geometry_msgs/Point.h
 /opt/ros/noetic/include/geometry_msgs/Quaternion.h
 /opt/ros/noetic/include/geometry_msgs/Vector3.h
 /opt/ros/noetic/include/std_msgs/ColorRGBA.h
 /home/<USER>/miniconda3/envs/coal/include/coal/math/transform.h
 /home/<USER>/miniconda3/envs/coal/include/coal/fwd.hh
 /home/<USER>/miniconda3/envs/coal/include/coal/config.hh
 /home/<USER>/miniconda3/envs/coal/include/coal/deprecated.hh
 /home/<USER>/miniconda3/envs/coal/include/coal/warning.hh
 /home/<USER>/miniconda3/envs/coal/include/coal/data_types.h
 /usr/include/eigen3/Eigen/Geometry
 /home/<USER>/miniconda3/envs/coal/include/coal/mesh_loader/loader.h
 /home/<USER>/miniconda3/envs/coal/include/coal/collision_object.h
 /home/<USER>/miniconda3/envs/coal/include/coal/BV/AABB.h
 /home/<USER>/miniconda3/envs/coal/include/coal/BVH/BVH_model.h
 /home/<USER>/miniconda3/envs/coal/include/coal/BVH/BVH_internal.h
 /home/<USER>/miniconda3/envs/coal/include/coal/BV/BV_node.h
 /home/<USER>/miniconda3/envs/coal/include/coal/BV/BV.h
 /home/<USER>/miniconda3/envs/coal/include/coal/BV/kDOP.h
 /home/<USER>/miniconda3/envs/coal/include/coal/BV/OBB.h
 /home/<USER>/miniconda3/envs/coal/include/coal/BV/RSS.h
 /home/<USER>/miniconda3/envs/coal/include/boost/math/constants/constants.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/math/tools/cxx03_warn.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/math/tools/convert_from_string.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/lexical_cast.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/lexical_cast/detail/buffer_view.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/lexical_cast/bad_lexical_cast.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/lexical_cast/try_lexical_convert.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/type_traits/conditional.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/lexical_cast/detail/is_character.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/type_traits/is_same.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/lexical_cast/detail/converter_numeric.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/core/cmath.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/limits.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/type_traits/type_identity.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/type_traits/make_unsigned.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/type_traits/is_signed.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/type_traits/remove_cv.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/type_traits/is_unsigned.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/type_traits/is_const.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/type_traits/is_volatile.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/type_traits/add_const.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/type_traits/add_volatile.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/type_traits/is_float.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/lexical_cast/detail/converter_lexical.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/detail/lcast_precision.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/integer_traits.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/lexical_cast/detail/widest_char.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/container/container_fwd.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/container/detail/workaround.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/container/detail/std_fwd.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/move/detail/std_ns_begin.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/move/detail/std_ns_end.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/lexical_cast/detail/converter_lexical_streams.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/core/snprintf.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/lexical_cast/detail/lcast_char_constants.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/lexical_cast/detail/lcast_unsigned_converters.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/core/noncopyable.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/lexical_cast/detail/lcast_basic_unlockedbuf.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/detail/basic_pointerbuf.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/integer.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/integer_fwd.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/lexical_cast/detail/inf_nan.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/type_traits/is_reference.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/type_traits/is_lvalue_reference.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/type_traits/is_rvalue_reference.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/math/constants/calculate_constants.hpp
 /home/<USER>/miniconda3/envs/coal/include/coal/BV/OBBRSS.h
 /home/<USER>/miniconda3/envs/coal/include/coal/BV/kIOS.h
 /home/<USER>/miniconda3/envs/coal/include/coal/collision.h
 /home/<USER>/miniconda3/envs/coal/include/coal/collision_data.h
 /usr/include/c++/11/numeric
 /usr/include/c++/11/bits/stl_numeric.h
 /usr/include/c++/11/pstl/glue_numeric_defs.h
 /home/<USER>/miniconda3/envs/coal/include/coal/timings.h
 /usr/include/c++/11/chrono
 /usr/include/c++/11/ratio
 /usr/include/c++/11/bits/parse_numbers.h
 /home/<USER>/miniconda3/envs/coal/include/coal/narrowphase/narrowphase_defaults.h
 /home/<USER>/miniconda3/envs/coal/include/coal/logging.h
 /home/<USER>/miniconda3/envs/coal/include/coal/collision_func_matrix.h
 /home/<USER>/miniconda3/envs/coal/include/coal/narrowphase/narrowphase.h
 /home/<USER>/miniconda3/envs/coal/include/coal/narrowphase/gjk.h
 /home/<USER>/miniconda3/envs/coal/include/coal/narrowphase/minkowski_difference.h
 /home/<USER>/miniconda3/envs/coal/include/coal/shape/geometric_shapes.h
 /home/<USER>/miniconda3/envs/coal/include/coal/narrowphase/support_functions.h
 /usr/include/c++/11/fstream
 /usr/include/x86_64-linux-gnu/c++/11/bits/basic_file.h
 /usr/include/x86_64-linux-gnu/c++/11/bits/c++io.h
 /usr/include/c++/11/bits/fstream.tcc
 /opt/ros/noetic/include/serial/serial.h
 /opt/ros/noetic/include/serial/v8stdint.h
 /usr/include/c++/11/mutex
 /usr/include/c++/11/bits/std_mutex.h
 /usr/include/c++/11/bits/unique_lock.h
 /usr/include/c++/11/future
 /usr/include/c++/11/condition_variable
 /usr/include/c++/11/atomic
 /usr/include/c++/11/bits/atomic_futex.h
 /usr/include/c++/11/bits/std_thread.h
 /usr/include/c++/11/thread
 /usr/include/c++/11/bits/this_thread_sleep.h
 /usr/include/kdl/kdl.hpp
 /usr/include/kdl/chain.hpp
 /usr/include/kdl/segment.hpp
 /usr/include/kdl/frames.hpp
 /usr/include/kdl/utilities/kdl-config.h
 /usr/include/kdl/utilities/utility.h
 /usr/include/kdl/utilities/kdl-config.h
 /usr/include/kdl/frames.inl
 /usr/include/kdl/rigidbodyinertia.hpp
 /usr/include/kdl/rotationalinertia.hpp
 /usr/include/kdl/joint.hpp
 /usr/include/kdl/tree.hpp
 /usr/include/kdl/config.h
 /usr/include/kdl/chain.hpp
 /opt/ros/noetic/include/kdl_parser/kdl_parser.hpp
 /usr/include/urdf_model/model.h
 /usr/include/urdf_model/link.h
 /usr/include/urdf_model/joint.h
 /usr/include/urdf_model/pose.h
 /usr/include/urdf_exception/exception.h
 /usr/include/urdf_model/utils.h
 /usr/include/urdf_model/types.h
 /usr/include/urdf_model/color.h
 /usr/include/urdf_model/types.h
 /usr/include/tinyxml2.h
 /usr/include/tinyxml.h
 /opt/ros/noetic/include/kdl_parser/visibility_control.hpp
 /usr/include/kdl/chainfksolverpos_recursive.hpp
 /usr/include/kdl/chainfksolver.hpp
 /usr/include/kdl/framevel.hpp
 /usr/include/kdl/utilities/rall1d.h
 /usr/include/kdl/utilities/utility.h
 /usr/include/kdl/utilities/traits.h
 /usr/include/kdl/framevel.inl
 /usr/include/kdl/frameacc.hpp
 /usr/include/kdl/utilities/rall2d.h
 /usr/include/kdl/frameacc.inl
 /usr/include/kdl/jntarray.hpp
 /usr/include/kdl/jacobian.hpp
 /usr/include/kdl/jntarrayvel.hpp
 /usr/include/kdl/jntarrayacc.hpp
 /usr/include/kdl/solveri.hpp
 /usr/include/kdl/chainiksolverpos_lma.hpp
 /usr/include/kdl/chainiksolver.hpp
 /usr/include/kdl/chainiksolver.hpp
 /usr/include/kdl/chainfksolver.hpp
 /opt/ros/noetic/include/trac_ik/trac_ik.hpp
 /opt/ros/noetic/include/trac_ik/nlopt_ik.hpp
 /opt/ros/noetic/include/trac_ik/kdl_tl.hpp
 /usr/include/kdl/chainiksolvervel_pinv.hpp
 /usr/include/kdl/chainjnttojacsolver.hpp
 /usr/include/kdl/utilities/svd_HH.hpp
 /usr/include/kdl/jacobian.hpp
 /usr/include/kdl/jntarray.hpp
 /usr/include/nlopt.hpp
 /usr/include/nlopt.h
 /usr/include/kdl/chainjnttojacsolver.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/date_time.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/date_time/local_time/local_time.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/date_time/posix_time/posix_time.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/date_time/compiler_config.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/date_time/locale_config.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/date_time/posix_time/ptime.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/date_time/posix_time/posix_time_system.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/date_time/posix_time/posix_time_config.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/config/no_tr1/cmath.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/date_time/time_duration.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/date_time/special_defs.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/date_time/time_defs.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/operators.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/date_time/time_resolution_traits.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/date_time/int_adapter.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/date_time/gregorian/gregorian_types.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/date_time/date.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/date_time/year_month_day.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/date_time/period.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/date_time/gregorian/greg_calendar.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/date_time/gregorian/greg_weekday.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/date_time/constrained_value.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/type_traits/is_base_of.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/type_traits/is_base_and_derived.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/type_traits/is_class.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/date_time/date_defs.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/date_time/gregorian/greg_day_of_year.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/date_time/gregorian_calendar.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/date_time/gregorian_calendar.ipp
 /home/<USER>/miniconda3/envs/coal/include/boost/date_time/gregorian/greg_ymd.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/date_time/gregorian/greg_day.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/date_time/gregorian/greg_year.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/date_time/gregorian/greg_month.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/date_time/gregorian/greg_duration.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/date_time/date_duration.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/date_time/date_duration_types.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/date_time/gregorian/greg_duration_types.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/date_time/gregorian/greg_date.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/date_time/adjust_functors.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/date_time/wrapping_int.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/date_time/date_generators.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/date_time/date_clock_device.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/date_time/c_time.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/date_time/date_iterator.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/date_time/time_system_split.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/date_time/time_system_counted.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/date_time/time.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/date_time/posix_time/date_duration_operators.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/date_time/posix_time/time_formatters.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/date_time/gregorian/gregorian.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/date_time/gregorian/conversion.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/date_time/gregorian/formatters.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/date_time/date_formatting.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/date_time/iso_format.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/date_time/parse_format_base.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/io/ios_state.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/io_fwd.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/date_time/date_format_simple.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/date_time/gregorian/gregorian_io.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/date_time/date_facet.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/algorithm/string/replace.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/algorithm/string/config.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/range/iterator_range_core.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/iterator/iterator_traits.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/iterator/iterator_facade.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/iterator/interoperable.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/type_traits/is_convertible.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/type_traits/is_complete.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/type_traits/declval.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/type_traits/add_rvalue_reference.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/type_traits/is_void.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/type_traits/is_function.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/type_traits/detail/is_function_cxx_11.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/type_traits/detail/yes_no_type.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/type_traits/is_array.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/type_traits/is_abstract.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/type_traits/add_lvalue_reference.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/type_traits/add_reference.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/iterator/detail/config_def.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/iterator/detail/config_undef.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/iterator/iterator_categories.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/mpl/eval_if.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/mpl/if.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/mpl/aux_/value_wknd.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/mpl/aux_/config/integral.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/mpl/identity.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/mpl/placeholders.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/mpl/arg.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/mpl/arg_fwd.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/mpl/aux_/na_assert.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/mpl/assert.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/mpl/aux_/yes_no.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/mpl/aux_/config/arrays.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/mpl/aux_/config/gpu.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/mpl/aux_/config/pp_counter.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/mpl/aux_/arity_spec.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/mpl/aux_/arg_typedef.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/mpl/aux_/preprocessed/gcc/arg.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/mpl/aux_/preprocessed/gcc/placeholders.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/iterator/detail/facade_iterator_category.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/core/use_default.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/detail/indirect_traits.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/type_traits/is_member_function_pointer.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/type_traits/detail/is_member_function_pointer_cxx_11.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/type_traits/is_member_pointer.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/type_traits/remove_pointer.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/detail/select_type.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/iterator/detail/enable_if.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/type_traits/add_pointer.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/type_traits/is_pod.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/type_traits/is_scalar.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/mpl/always.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/mpl/aux_/preprocessor/default_params.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/mpl/apply.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/mpl/apply_fwd.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/mpl/aux_/preprocessed/gcc/apply_fwd.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/mpl/apply_wrap.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/mpl/aux_/has_apply.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/mpl/has_xxx.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/mpl/aux_/type_wrapper.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/mpl/aux_/config/has_xxx.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/mpl/aux_/config/msvc_typename.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/preprocessor/array/elem.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/preprocessor/array/data.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/preprocessor/array/size.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/preprocessor/repetition/enum_params.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/preprocessor/repetition/enum_trailing_params.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/mpl/aux_/config/has_apply.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/mpl/aux_/msvc_never_true.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/mpl/aux_/preprocessed/gcc/apply_wrap.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/mpl/lambda.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/mpl/bind.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/mpl/bind_fwd.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/mpl/aux_/config/bind.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/mpl/aux_/preprocessed/gcc/bind_fwd.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/mpl/next.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/mpl/next_prior.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/mpl/aux_/common_name_wknd.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/mpl/protect.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/mpl/aux_/preprocessed/gcc/bind.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/mpl/aux_/full_lambda.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/mpl/quote.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/mpl/void.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/mpl/aux_/has_type.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/mpl/aux_/config/bcc.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/mpl/aux_/preprocessed/gcc/quote.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/mpl/aux_/template_arity.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/mpl/aux_/preprocessed/gcc/template_arity.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/mpl/aux_/preprocessed/gcc/full_lambda.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/mpl/aux_/preprocessed/gcc/apply.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/range/functions.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/range/begin.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/range/config.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/range/iterator.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/range/range_fwd.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/range/mutable_iterator.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/range/detail/extract_optional_type.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/range/detail/msvc_has_iterator_workaround.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/range/const_iterator.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/range/end.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/range/detail/implementation_help.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/range/detail/common.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/range/detail/sfinae.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/range/size.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/range/size_type.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/range/difference_type.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/range/has_range_iterator.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/range/concepts.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/concept_check.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/concept/assert.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/concept/detail/general.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/concept/detail/backward_compatibility.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/concept/detail/has_constraints.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/type_traits/conversion_traits.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/concept/usage.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/concept/detail/concept_def.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/preprocessor/seq/for_each_i.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/preprocessor/repetition/for.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/preprocessor/repetition/detail/for.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/preprocessor/repetition/detail/limits/for_256.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/preprocessor/repetition/limits/for_256.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/preprocessor/seq/seq.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/preprocessor/seq/elem.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/preprocessor/seq/limits/elem_256.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/preprocessor/seq/size.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/preprocessor/seq/limits/size_256.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/preprocessor/seq/detail/is_empty.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/preprocessor/seq/enum.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/preprocessor/seq/limits/enum_256.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/concept/detail/concept_undef.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/iterator/iterator_concepts.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/range/value_type.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/range/detail/misc_concept.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/range/detail/has_member_size.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/utility.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/utility/base_from_member.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/preprocessor/repetition/enum_binary_params.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/preprocessor/repetition/repeat_from_to.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/utility/binary.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/preprocessor/control/deduce_d.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/preprocessor/seq/cat.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/preprocessor/seq/fold_left.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/preprocessor/seq/limits/fold_left_256.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/preprocessor/seq/transform.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/preprocessor/arithmetic/mod.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/preprocessor/arithmetic/detail/div_base.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/preprocessor/comparison/less_equal.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/preprocessor/arithmetic/detail/is_1_number.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/utility/identity_type.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/type_traits/function_traits.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/range/distance.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/iterator/distance.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/range/empty.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/range/rbegin.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/range/reverse_iterator.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/iterator/reverse_iterator.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/iterator/iterator_adaptor.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/range/rend.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/range/algorithm/equal.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/range/detail/safe_bool.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/next_prior.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/type_traits/has_plus.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/type_traits/detail/has_binary_operator.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/type_traits/make_void.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/type_traits/has_plus_assign.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/type_traits/has_minus.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/type_traits/has_minus_assign.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/iterator/is_iterator.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/type_traits/negation.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/type_traits/conjunction.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/iterator/advance.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/algorithm/string/find_format.hpp
 /usr/include/c++/11/deque
 /usr/include/c++/11/bits/stl_deque.h
 /usr/include/c++/11/bits/deque.tcc
 /home/<USER>/miniconda3/envs/coal/include/boost/range/as_literal.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/range/iterator_range.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/range/iterator_range_io.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/range/detail/str_types.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/algorithm/string/concept.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/algorithm/string/detail/find_format.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/algorithm/string/detail/find_format_store.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/algorithm/string/detail/replace_storage.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/algorithm/string/sequence_traits.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/algorithm/string/yes_no_type.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/algorithm/string/detail/sequence.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/mpl/logical.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/algorithm/string/detail/find_format_all.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/algorithm/string/finder.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/algorithm/string/constants.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/algorithm/string/detail/finder.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/algorithm/string/compare.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/algorithm/string/formatter.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/algorithm/string/detail/formatter.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/algorithm/string/detail/util.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/date_time/special_values_formatter.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/date_time/period_formatter.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/date_time/period_parser.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/date_time/string_parse_tree.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/algorithm/string/case_conv.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/iterator/transform_iterator.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/utility/result_of.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/utility/detail/result_of_variadic.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/algorithm/string/detail/case_conv.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/date_time/string_convert.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/date_time/date_generator_formatter.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/date_time/date_generator_parser.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/date_time/format_date_parser.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/date_time/strings_from_facet.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/date_time/special_values_parser.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/date_time/gregorian/parsers.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/date_time/date_parsing.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/tokenizer.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/token_iterator.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/iterator/minimum_category.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/token_functions.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/date_time/find_match.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/date_time/posix_time/posix_time_types.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/date_time/time_clock.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/date_time/microsec_time_clock.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/date_time/posix_time/posix_time_duration.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/numeric/conversion/cast.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/type.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/numeric/conversion/converter.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/numeric/conversion/conversion_traits.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/numeric/conversion/detail/conversion_traits.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/numeric/conversion/detail/meta.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/mpl/equal_to.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/mpl/aux_/comparison_op.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/mpl/aux_/numeric_op.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/mpl/numeric_cast.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/mpl/tag.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/mpl/aux_/has_tag.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/mpl/aux_/numeric_cast_utils.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/mpl/aux_/config/forwarding.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/mpl/aux_/msvc_eti_base.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/mpl/aux_/is_msvc_eti_arg.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/mpl/aux_/preprocessed/gcc/equal_to.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/numeric/conversion/detail/int_float_mixture.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/numeric/conversion/int_float_mixture_enum.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/numeric/conversion/detail/sign_mixture.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/numeric/conversion/sign_mixture_enum.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/numeric/conversion/detail/udt_builtin_mixture.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/numeric/conversion/udt_builtin_mixture_enum.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/numeric/conversion/detail/is_subranged.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/mpl/multiplies.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/mpl/times.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/mpl/aux_/arithmetic_op.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/mpl/integral_c.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/mpl/integral_c_fwd.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/mpl/aux_/largest_int.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/mpl/aux_/preprocessed/gcc/times.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/mpl/less.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/mpl/aux_/preprocessed/gcc/less.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/numeric/conversion/converter_policies.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/numeric/conversion/detail/converter.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/numeric/conversion/bounds.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/numeric/conversion/detail/bounds.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/numeric/conversion/numeric_cast_traits.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/numeric/conversion/detail/numeric_cast_traits.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/numeric/conversion/detail/preprocessed/numeric_cast_traits_common.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/numeric/conversion/detail/preprocessed/numeric_cast_traits_long_long.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/date_time/posix_time/time_period.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/date_time/time_iterator.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/date_time/dst_rules.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/date_time/time_formatting_streams.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/date_time/date_formatting_locales.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/date_time/date_names_put.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/date_time/time_parsing.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/date_time/posix_time/posix_time_io.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/date_time/time_facet.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/algorithm/string/erase.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/date_time/posix_time/conversion.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/date_time/filetime_functions.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/date_time/posix_time/time_parsers.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/date_time/local_time/local_date_time.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/date_time/time_zone_base.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/date_time/local_time/local_time_types.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/date_time/local_time/date_duration_operators.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/date_time/local_time/custom_time_zone.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/date_time/time_zone_names.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/date_time/local_time/dst_transition_day_rules.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/date_time/dst_transition_generators.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/date_time/local_time/local_time_io.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/date_time/local_time/posix_time_zone.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/date_time/local_time/conversion.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/date_time/local_time/tz_database.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/date_time/tz_db_base.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/algorithm/string.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/algorithm/string/std_containers_traits.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/algorithm/string/std/string_traits.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/algorithm/string/std/list_traits.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/algorithm/string/std/slist_traits.hpp
 /usr/include/c++/11/ext/slist
 /home/<USER>/miniconda3/envs/coal/include/boost/algorithm/string/trim.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/algorithm/string/detail/trim.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/algorithm/string/classification.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/algorithm/string/detail/classification.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/algorithm/string/predicate_facade.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/algorithm/string/predicate.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/algorithm/string/find.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/algorithm/string/detail/predicate.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/algorithm/string/split.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/algorithm/string/iter_find.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/algorithm/string/find_iterator.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/algorithm/string/detail/find_iterator.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/function.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/function/function_template.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/function/function_base.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/function/function_fwd.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/function_equal.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/core/typeinfo.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/core/demangle.hpp
 /usr/include/c++/11/cxxabi.h
 /usr/include/x86_64-linux-gnu/c++/11/bits/cxxabi_tweaks.h
 /home/<USER>/miniconda3/envs/coal/include/boost/core/ref.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/core/no_exceptions_support.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/mem_fn.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/bind/mem_fn.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/get_pointer.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/config/no_tr1/memory.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/algorithm/string/join.hpp
 /opt/ros/noetic/include/urdf/model.h
 /opt/ros/noetic/include/urdf/urdfdom_compatibility.h
 /usr/include/urdf_world/types.h
 /opt/ros/noetic/include/ros/ros.h
 /opt/ros/noetic/include/ros/rate.h
 /opt/ros/noetic/include/ros/console.h
 /opt/ros/noetic/include/ros/console_backend.h
 /usr/include/c++/11/cstdarg
 /usr/include/log4cxx/level.h
 /usr/include/log4cxx/logstring.h
 /usr/include/log4cxx/log4cxx.h
 /usr/include/log4cxx/helpers/transcoder.h
 /usr/include/log4cxx/helpers/objectimpl.h
 /usr/include/log4cxx/helpers/object.h
 /usr/include/log4cxx/helpers/class.h
 /usr/include/log4cxx/helpers/objectptr.h
 /usr/include/log4cxx/helpers/classregistration.h
 /opt/ros/noetic/include/rosconsole/macros_generated.h
 /opt/ros/noetic/include/ros/assert.h
 /opt/ros/noetic/include/ros/static_assert.h
 /opt/ros/noetic/include/ros/common.h
 /opt/ros/noetic/include/ros/forwards.h
 /home/<USER>/miniconda3/envs/coal/include/boost/make_shared.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/smart_ptr/make_shared.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/smart_ptr/make_shared_object.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/move/core.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/move/detail/config_begin.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/move/detail/workaround.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/move/detail/config_end.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/move/utility_core.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/move/detail/meta_utils.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/move/detail/meta_utils_core.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/move/detail/addressof.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/smart_ptr/detail/sp_forward.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/type_traits/type_with_alignment.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/type_traits/alignment_of.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/smart_ptr/make_shared_array.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/core/default_allocator.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/smart_ptr/allocate_shared_array.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/core/allocator_access.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/core/pointer_traits.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/core/alloc_construct.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/core/noinit_adaptor.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/core/first_scalar.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/type_traits/enable_if.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/type_traits/extent.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/type_traits/is_bounded_array.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/type_traits/is_unbounded_array.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/type_traits/remove_extent.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/weak_ptr.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/smart_ptr/weak_ptr.hpp
 /opt/ros/noetic/include/ros/exceptions.h
 /opt/ros/noetic/include/ros/serialized_message.h
 /opt/ros/noetic/include/ros/node_handle.h
 /opt/ros/noetic/include/ros/publisher.h
 /opt/ros/noetic/include/ros/message.h
 /home/<USER>/miniconda3/envs/coal/include/boost/bind/bind.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/bind/arg.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/is_placeholder.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/bind/std_placeholders.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/bind/detail/result_traits.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/bind/detail/tuple_for_each.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/bind/detail/integer_sequence.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/visit_each.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/bind/detail/bind_cc.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/bind/detail/bind_mf_cc.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/bind/detail/bind_mf2_cc.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/bind/placeholders.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/thread/mutex.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/thread/detail/platform.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/config/requires_threads.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/thread/pthread/mutex.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/thread/detail/config.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/thread/detail/thread_safety.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/config/auto_link.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/core/ignore_unused.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/thread/exceptions.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/system/system_error.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/system/errc.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/system/detail/errc.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/system/is_error_condition_enum.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/system/detail/cerrno.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/system/detail/error_code.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/system/is_error_code_enum.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/system/detail/error_category.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/system/detail/config.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/system/detail/error_condition.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/system/detail/generic_category.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/system/detail/generic_category_message.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/system/detail/enable_if.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/system/detail/is_same.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/system/detail/append_int.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/system/detail/snprintf.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/system/detail/system_category.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/system/detail/system_category_impl.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/system/detail/system_category_message.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/system/api_config.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/system/detail/interop_category.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/system/detail/std_category.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/system/detail/error_category_impl.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/system/detail/std_category_impl.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/system/detail/mutex.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/system/error_code.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/system/error_category.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/system/error_condition.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/system/generic_category.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/system/system_category.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/system/detail/throws.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/config/abi_prefix.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/config/abi_suffix.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/thread/lock_types.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/thread/detail/move.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/type_traits/decay.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/type_traits/remove_bounds.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/thread/detail/delete.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/move/utility.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/move/traits.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/move/detail/type_traits.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/thread/lock_options.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/thread/lockable_traits.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/thread/thread_time.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/chrono/time_point.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/chrono/duration.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/chrono/config.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/chrono/detail/requires_cxx11.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/predef.h
 /home/<USER>/miniconda3/envs/coal/include/boost/predef/language.h
 /home/<USER>/miniconda3/envs/coal/include/boost/predef/language/stdc.h
 /home/<USER>/miniconda3/envs/coal/include/boost/predef/language/stdcpp.h
 /home/<USER>/miniconda3/envs/coal/include/boost/predef/language/objc.h
 /home/<USER>/miniconda3/envs/coal/include/boost/predef/language/cuda.h
 /home/<USER>/miniconda3/envs/coal/include/boost/predef/architecture.h
 /home/<USER>/miniconda3/envs/coal/include/boost/predef/architecture/alpha.h
 /home/<USER>/miniconda3/envs/coal/include/boost/predef/architecture/arm.h
 /home/<USER>/miniconda3/envs/coal/include/boost/predef/architecture/blackfin.h
 /home/<USER>/miniconda3/envs/coal/include/boost/predef/architecture/convex.h
 /home/<USER>/miniconda3/envs/coal/include/boost/predef/architecture/e2k.h
 /home/<USER>/miniconda3/envs/coal/include/boost/predef/architecture/ia64.h
 /home/<USER>/miniconda3/envs/coal/include/boost/predef/architecture/loongarch.h
 /home/<USER>/miniconda3/envs/coal/include/boost/predef/architecture/m68k.h
 /home/<USER>/miniconda3/envs/coal/include/boost/predef/architecture/mips.h
 /home/<USER>/miniconda3/envs/coal/include/boost/predef/architecture/parisc.h
 /home/<USER>/miniconda3/envs/coal/include/boost/predef/architecture/ppc.h
 /home/<USER>/miniconda3/envs/coal/include/boost/predef/architecture/ptx.h
 /home/<USER>/miniconda3/envs/coal/include/boost/predef/architecture/pyramid.h
 /home/<USER>/miniconda3/envs/coal/include/boost/predef/architecture/riscv.h
 /home/<USER>/miniconda3/envs/coal/include/boost/predef/architecture/rs6k.h
 /home/<USER>/miniconda3/envs/coal/include/boost/predef/architecture/sparc.h
 /home/<USER>/miniconda3/envs/coal/include/boost/predef/architecture/superh.h
 /home/<USER>/miniconda3/envs/coal/include/boost/predef/architecture/sys370.h
 /home/<USER>/miniconda3/envs/coal/include/boost/predef/architecture/sys390.h
 /home/<USER>/miniconda3/envs/coal/include/boost/predef/architecture/x86.h
 /home/<USER>/miniconda3/envs/coal/include/boost/predef/architecture/x86/32.h
 /home/<USER>/miniconda3/envs/coal/include/boost/predef/architecture/x86/64.h
 /home/<USER>/miniconda3/envs/coal/include/boost/predef/architecture/z.h
 /home/<USER>/miniconda3/envs/coal/include/boost/predef/compiler.h
 /home/<USER>/miniconda3/envs/coal/include/boost/predef/compiler/borland.h
 /home/<USER>/miniconda3/envs/coal/include/boost/predef/compiler/clang.h
 /home/<USER>/miniconda3/envs/coal/include/boost/predef/compiler/comeau.h
 /home/<USER>/miniconda3/envs/coal/include/boost/predef/compiler/compaq.h
 /home/<USER>/miniconda3/envs/coal/include/boost/predef/compiler/diab.h
 /home/<USER>/miniconda3/envs/coal/include/boost/predef/compiler/digitalmars.h
 /home/<USER>/miniconda3/envs/coal/include/boost/predef/compiler/dignus.h
 /home/<USER>/miniconda3/envs/coal/include/boost/predef/compiler/edg.h
 /home/<USER>/miniconda3/envs/coal/include/boost/predef/compiler/ekopath.h
 /home/<USER>/miniconda3/envs/coal/include/boost/predef/compiler/gcc_xml.h
 /home/<USER>/miniconda3/envs/coal/include/boost/predef/compiler/gcc.h
 /home/<USER>/miniconda3/envs/coal/include/boost/predef/detail/comp_detected.h
 /home/<USER>/miniconda3/envs/coal/include/boost/predef/compiler/greenhills.h
 /home/<USER>/miniconda3/envs/coal/include/boost/predef/compiler/hp_acc.h
 /home/<USER>/miniconda3/envs/coal/include/boost/predef/compiler/iar.h
 /home/<USER>/miniconda3/envs/coal/include/boost/predef/compiler/ibm.h
 /home/<USER>/miniconda3/envs/coal/include/boost/predef/compiler/intel.h
 /home/<USER>/miniconda3/envs/coal/include/boost/predef/compiler/kai.h
 /home/<USER>/miniconda3/envs/coal/include/boost/predef/compiler/llvm.h
 /home/<USER>/miniconda3/envs/coal/include/boost/predef/compiler/metaware.h
 /home/<USER>/miniconda3/envs/coal/include/boost/predef/compiler/metrowerks.h
 /home/<USER>/miniconda3/envs/coal/include/boost/predef/compiler/microtec.h
 /home/<USER>/miniconda3/envs/coal/include/boost/predef/compiler/mpw.h
 /home/<USER>/miniconda3/envs/coal/include/boost/predef/compiler/nvcc.h
 /home/<USER>/miniconda3/envs/coal/include/boost/predef/compiler/palm.h
 /home/<USER>/miniconda3/envs/coal/include/boost/predef/compiler/pgi.h
 /home/<USER>/miniconda3/envs/coal/include/boost/predef/compiler/sgi_mipspro.h
 /home/<USER>/miniconda3/envs/coal/include/boost/predef/compiler/sunpro.h
 /home/<USER>/miniconda3/envs/coal/include/boost/predef/compiler/tendra.h
 /home/<USER>/miniconda3/envs/coal/include/boost/predef/compiler/visualc.h
 /home/<USER>/miniconda3/envs/coal/include/boost/predef/compiler/watcom.h
 /home/<USER>/miniconda3/envs/coal/include/boost/predef/library.h
 /home/<USER>/miniconda3/envs/coal/include/boost/predef/library/c.h
 /home/<USER>/miniconda3/envs/coal/include/boost/predef/library/c/cloudabi.h
 /home/<USER>/miniconda3/envs/coal/include/boost/predef/library/c/uc.h
 /home/<USER>/miniconda3/envs/coal/include/boost/predef/library/c/vms.h
 /home/<USER>/miniconda3/envs/coal/include/boost/predef/library/c/zos.h
 /home/<USER>/miniconda3/envs/coal/include/boost/predef/library/std.h
 /home/<USER>/miniconda3/envs/coal/include/boost/predef/library/std/_prefix.h
 /home/<USER>/miniconda3/envs/coal/include/boost/predef/detail/_exception.h
 /home/<USER>/miniconda3/envs/coal/include/boost/predef/library/std/cxx.h
 /home/<USER>/miniconda3/envs/coal/include/boost/predef/library/std/dinkumware.h
 /home/<USER>/miniconda3/envs/coal/include/boost/predef/library/std/libcomo.h
 /home/<USER>/miniconda3/envs/coal/include/boost/predef/library/std/modena.h
 /home/<USER>/miniconda3/envs/coal/include/boost/predef/library/std/msl.h
 /home/<USER>/miniconda3/envs/coal/include/boost/predef/library/std/msvc.h
 /home/<USER>/miniconda3/envs/coal/include/boost/predef/library/std/roguewave.h
 /home/<USER>/miniconda3/envs/coal/include/boost/predef/library/std/sgi.h
 /home/<USER>/miniconda3/envs/coal/include/boost/predef/library/std/stdcpp3.h
 /home/<USER>/miniconda3/envs/coal/include/boost/predef/library/std/stlport.h
 /home/<USER>/miniconda3/envs/coal/include/boost/predef/library/std/vacpp.h
 /home/<USER>/miniconda3/envs/coal/include/boost/predef/os.h
 /home/<USER>/miniconda3/envs/coal/include/boost/predef/os/aix.h
 /home/<USER>/miniconda3/envs/coal/include/boost/predef/os/amigaos.h
 /home/<USER>/miniconda3/envs/coal/include/boost/predef/os/beos.h
 /home/<USER>/miniconda3/envs/coal/include/boost/predef/os/cygwin.h
 /home/<USER>/miniconda3/envs/coal/include/boost/predef/os/haiku.h
 /home/<USER>/miniconda3/envs/coal/include/boost/predef/os/hpux.h
 /home/<USER>/miniconda3/envs/coal/include/boost/predef/os/irix.h
 /home/<USER>/miniconda3/envs/coal/include/boost/predef/os/linux.h
 /home/<USER>/miniconda3/envs/coal/include/boost/predef/detail/os_detected.h
 /home/<USER>/miniconda3/envs/coal/include/boost/predef/os/os400.h
 /home/<USER>/miniconda3/envs/coal/include/boost/predef/os/qnxnto.h
 /home/<USER>/miniconda3/envs/coal/include/boost/predef/os/solaris.h
 /home/<USER>/miniconda3/envs/coal/include/boost/predef/os/unix.h
 /home/<USER>/miniconda3/envs/coal/include/boost/predef/os/vms.h
 /home/<USER>/miniconda3/envs/coal/include/boost/predef/os/windows.h
 /home/<USER>/miniconda3/envs/coal/include/boost/predef/other.h
 /home/<USER>/miniconda3/envs/coal/include/boost/predef/other/wordsize.h
 /home/<USER>/miniconda3/envs/coal/include/boost/predef/other/workaround.h
 /home/<USER>/miniconda3/envs/coal/include/boost/predef/platform.h
 /home/<USER>/miniconda3/envs/coal/include/boost/predef/platform/cloudabi.h
 /home/<USER>/miniconda3/envs/coal/include/boost/predef/platform/mingw.h
 /home/<USER>/miniconda3/envs/coal/include/boost/predef/platform/mingw32.h
 /home/<USER>/miniconda3/envs/coal/include/boost/predef/platform/mingw64.h
 /home/<USER>/miniconda3/envs/coal/include/boost/predef/platform/windows_uwp.h
 /home/<USER>/miniconda3/envs/coal/include/boost/predef/platform/windows_desktop.h
 /home/<USER>/miniconda3/envs/coal/include/boost/predef/platform/windows_phone.h
 /home/<USER>/miniconda3/envs/coal/include/boost/predef/platform/windows_server.h
 /home/<USER>/miniconda3/envs/coal/include/boost/predef/platform/windows_store.h
 /home/<USER>/miniconda3/envs/coal/include/boost/predef/platform/windows_system.h
 /home/<USER>/miniconda3/envs/coal/include/boost/predef/platform/windows_runtime.h
 /home/<USER>/miniconda3/envs/coal/include/boost/predef/platform/ios.h
 /home/<USER>/miniconda3/envs/coal/include/boost/predef/hardware.h
 /home/<USER>/miniconda3/envs/coal/include/boost/predef/hardware/simd.h
 /home/<USER>/miniconda3/envs/coal/include/boost/predef/hardware/simd/x86.h
 /home/<USER>/miniconda3/envs/coal/include/boost/predef/hardware/simd/x86/versions.h
 /home/<USER>/miniconda3/envs/coal/include/boost/predef/hardware/simd/x86_amd.h
 /home/<USER>/miniconda3/envs/coal/include/boost/predef/hardware/simd/x86_amd/versions.h
 /home/<USER>/miniconda3/envs/coal/include/boost/predef/hardware/simd/arm.h
 /home/<USER>/miniconda3/envs/coal/include/boost/predef/hardware/simd/arm/versions.h
 /home/<USER>/miniconda3/envs/coal/include/boost/predef/hardware/simd/ppc.h
 /home/<USER>/miniconda3/envs/coal/include/boost/predef/hardware/simd/ppc/versions.h
 /home/<USER>/miniconda3/envs/coal/include/boost/predef/version.h
 /home/<USER>/miniconda3/envs/coal/include/boost/chrono/detail/static_assert.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/ratio/ratio.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/ratio/ratio_fwd.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/ratio/detail/gcd_lcm.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/ratio/detail/is_ratio.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/type_traits/common_type.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/type_traits/detail/mp_defer.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/chrono/detail/is_evenly_divisible_by.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/ratio/detail/is_evenly_divisible_by.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/thread/xtime.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/thread/detail/platform_time.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/chrono/system_clocks.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/chrono/detail/system.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/chrono/clock_string.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/ratio/config.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/chrono/ceil.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/thread/pthread/pthread_mutex_scoped_lock.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/thread/pthread/pthread_helpers.hpp
 /opt/ros/noetic/include/ros/subscriber.h
 /opt/ros/noetic/include/ros/common.h
 /opt/ros/noetic/include/ros/subscription_callback_helper.h
 /opt/ros/noetic/include/ros/parameter_adapter.h
 /opt/ros/noetic/include/ros/message_event.h
 /opt/ros/noetic/include/ros/service_server.h
 /opt/ros/noetic/include/ros/service_client.h
 /opt/ros/noetic/include/ros/service_traits.h
 /opt/ros/noetic/include/ros/timer.h
 /opt/ros/noetic/include/ros/forwards.h
 /opt/ros/noetic/include/ros/timer_options.h
 /opt/ros/noetic/include/ros/wall_timer.h
 /opt/ros/noetic/include/ros/wall_timer_options.h
 /opt/ros/noetic/include/ros/steady_timer.h
 /opt/ros/noetic/include/ros/steady_timer_options.h
 /opt/ros/noetic/include/ros/advertise_options.h
 /opt/ros/noetic/include/ros/advertise_service_options.h
 /opt/ros/noetic/include/ros/service_callback_helper.h
 /opt/ros/noetic/include/ros/subscribe_options.h
 /opt/ros/noetic/include/ros/transport_hints.h
 /opt/ros/noetic/include/ros/subscription_callback_helper.h
 /opt/ros/noetic/include/ros/service_client_options.h
 /opt/ros/noetic/include/ros/timer_options.h
 /opt/ros/noetic/include/ros/wall_timer_options.h
 /opt/ros/noetic/include/ros/spinner.h
 /opt/ros/noetic/include/ros/init.h
 /opt/ros/noetic/include/xmlrpcpp/XmlRpcValue.h
 /opt/ros/noetic/include/xmlrpcpp/XmlRpcDecl.h
 /opt/ros/noetic/include/ros/single_subscriber_publisher.h
 /opt/ros/noetic/include/ros/service.h
 /opt/ros/noetic/include/ros/names.h
 /opt/ros/noetic/include/ros/master.h
 /opt/ros/noetic/include/ros/this_node.h
 /opt/ros/noetic/include/ros/param.h
 /opt/ros/noetic/include/ros/topic.h
 /opt/ros/noetic/include/ros/node_handle.h
 /opt/ros/noetic/include/urdf/visibility_control.hpp
 /usr/include/yaml-cpp/yaml.h
 /usr/include/yaml-cpp/parser.h
 /usr/include/yaml-cpp/dll.h
 /usr/include/yaml-cpp/noncopyable.h
 /usr/include/yaml-cpp/emitter.h
 /usr/include/yaml-cpp/binary.h
 /usr/include/yaml-cpp/emitterdef.h
 /usr/include/yaml-cpp/emittermanip.h
 /usr/include/yaml-cpp/null.h
 /usr/include/yaml-cpp/ostream_wrapper.h
 /usr/include/yaml-cpp/emitterstyle.h
 /usr/include/yaml-cpp/stlemitter.h
 /usr/include/yaml-cpp/exceptions.h
 /usr/include/yaml-cpp/mark.h
 /usr/include/yaml-cpp/traits.h
 /usr/include/yaml-cpp/node/node.h
 /usr/include/yaml-cpp/node/detail/bool_type.h
 /usr/include/yaml-cpp/node/detail/iterator_fwd.h
 /usr/include/yaml-cpp/node/ptr.h
 /usr/include/yaml-cpp/node/type.h
 /usr/include/yaml-cpp/node/impl.h
 /usr/include/yaml-cpp/node/iterator.h
 /usr/include/yaml-cpp/node/detail/iterator.h
 /usr/include/yaml-cpp/node/detail/node_iterator.h
 /usr/include/yaml-cpp/node/detail/memory.h
 /usr/include/yaml-cpp/node/detail/node.h
 /usr/include/yaml-cpp/node/detail/node_ref.h
 /usr/include/yaml-cpp/node/detail/node_data.h
 /usr/include/yaml-cpp/node/convert.h
 /usr/include/yaml-cpp/node/detail/impl.h
 /usr/include/yaml-cpp/node/parse.h
 /usr/include/yaml-cpp/node/emit.h
 /home/<USER>/workspace/arm_control_ws/src/robot_controller/include/relaxed_ik_wrapper/relaxed_ik_wrapper.h
 /opt/ros/noetic/include/tf2/LinearMath/Quaternion.h
 /opt/ros/noetic/include/tf2/LinearMath/Vector3.h
 /opt/ros/noetic/include/tf2/LinearMath/Scalar.h
 /opt/ros/noetic/include/tf2/LinearMath/MinMax.h
 /opt/ros/noetic/include/tf2/LinearMath/QuadWord.h
 /opt/ros/noetic/include/tf2/LinearMath/Matrix3x3.h
 /opt/ros/noetic/include/tf2/LinearMath/Quaternion.h
 /opt/ros/noetic/include/std_msgs/Int8.h
 /opt/ros/noetic/include/std_msgs/Bool.h
 /opt/ros/noetic/include/std_msgs/Float64MultiArray.h
 /opt/ros/noetic/include/std_msgs/MultiArrayLayout.h
 /opt/ros/noetic/include/std_msgs/MultiArrayDimension.h
 /opt/ros/noetic/include/std_msgs/Float32MultiArray.h
 /opt/ros/noetic/include/std_msgs/Float64.h
 /opt/ros/noetic/include/std_msgs/Float32.h
 /opt/ros/noetic/include/sensor_msgs/JointState.h
 /opt/ros/noetic/include/geometry_msgs/PoseArray.h
 /opt/ros/noetic/include/geometry_msgs/PoseStamped.h
 /home/<USER>/workspace/arm_control_ws/devel/include/robot_controller/ClothePoint.h
 /home/<USER>/workspace/arm_control_ws/devel/include/robot_controller/ClothePointRequest.h
 /home/<USER>/workspace/arm_control_ws/devel/include/robot_controller/ClothePointResponse.h
 /opt/ros/noetic/include/ros/package.h
 /opt/ros/noetic/include/ros/callback_queue.h
 /opt/ros/noetic/include/ros/callback_queue_interface.h
 /home/<USER>/miniconda3/envs/coal/include/boost/thread/condition_variable.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/thread/pthread/condition_variable.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/thread/interruption.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/thread/pthread/thread_data.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/thread/lock_guard.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/thread/detail/lockable_wrapper.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/thread/pthread/condition_variable_fwd.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/thread/cv_status.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/core/scoped_enum.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/enable_shared_from_this.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/smart_ptr/enable_shared_from_this.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/thread/shared_mutex.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/thread/pthread/shared_mutex.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/thread/detail/thread_interruption.hpp
 /home/<USER>/miniconda3/envs/coal/include/boost/thread/tss.hpp

