# generated from genmsg/cmake/pkg-genmsg.context.in

messages_str = ""
services_str = ""
pkg_name = "pose_detec_mediapipe"
dependencies_str = ""
langs = "gencpp;geneus;genlisp;gennodejs;genpy"
dep_include_paths_str = ""
PYTHON_EXECUTABLE = "/home/<USER>/miniconda3/envs/mediapipe/bin/python"
package_has_static_sources = '' == 'TRUE'
genmsg_check_deps_script = "/opt/ros/noetic/share/genmsg/cmake/../../../lib/genmsg/genmsg_check_deps.py"
