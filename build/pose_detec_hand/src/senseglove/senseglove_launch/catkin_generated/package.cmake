set(_CATKIN_CURRENT_PACKAGE "senseglove_launch")
set(senseglove_launch_VERSION "2.1.0")
set(senseglove_launch_MAINTAINER "SenseGlove <<EMAIL>>")
set(senseglove_launch_PACKAGE_FORMAT "3")
set(senseglove_launch_BUILD_DEPENDS )
set(senseglove_launch_BUILD_EXPORT_DEPENDS )
set(senseglove_launch_BUILDTOOL_DEPENDS "catkin")
set(senseglove_launch_BUILDTOOL_EXPORT_DEPENDS )
set(senseglove_launch_EXEC_DEPENDS "senseglove_description" "senseglove_finger_distance" "senseglove_hardware" "senseglove_hardware_interface" "rviz" "xacro")
set(senseglove_launch_RUN_DEPENDS "senseglove_description" "senseglove_finger_distance" "senseglove_hardware" "senseglove_hardware_interface" "rviz" "xacro")
set(senseglove_launch_TEST_DEPENDS )
set(senseglove_launch_DOC_DEPENDS )
set(senseglove_launch_URL_WEBSITE "")
set(senseglove_launch_URL_BUGTRACKER "")
set(senseglove_launch_URL_REPOSITORY "")
set(senseglove_launch_DEPRECATED "")