# generated from genmsg/cmake/pkg-genmsg.context.in

messages_str = "/home/<USER>/workspace/arm_control_ws/src/pose_detec_hand/src/senseglove/senseglove_shared_resources/msg/FingerDistanceFloats.msg;/home/<USER>/workspace/arm_control_ws/src/pose_detec_hand/src/senseglove/senseglove_shared_resources/msg/FingerDistances.msg;/home/<USER>/workspace/arm_control_ws/src/pose_detec_hand/src/senseglove/senseglove_shared_resources/msg/KinematicsVect3D.msg;/home/<USER>/workspace/arm_control_ws/src/pose_detec_hand/src/senseglove/senseglove_shared_resources/msg/SenseGloveState.msg"
services_str = "/home/<USER>/workspace/arm_control_ws/src/pose_detec_hand/src/senseglove/senseglove_shared_resources/srv/Calibrate.srv"
pkg_name = "senseglove_shared_resources"
dependencies_str = "actionlib_msgs;control_msgs;std_msgs;trajectory_msgs"
langs = "gencpp;geneus;genlisp;gennodejs;genpy"
dep_include_paths_str = "senseglove_shared_resources;/home/<USER>/workspace/arm_control_ws/src/pose_detec_hand/src/senseglove/senseglove_shared_resources/msg;actionlib_msgs;/opt/ros/noetic/share/actionlib_msgs/cmake/../msg;control_msgs;/opt/ros/noetic/share/control_msgs/cmake/../msg;std_msgs;/opt/ros/noetic/share/std_msgs/cmake/../msg;trajectory_msgs;/opt/ros/noetic/share/trajectory_msgs/cmake/../msg;geometry_msgs;/opt/ros/noetic/share/geometry_msgs/cmake/../msg"
PYTHON_EXECUTABLE = "/usr/bin/python3"
package_has_static_sources = '' == 'TRUE'
genmsg_check_deps_script = "/opt/ros/noetic/share/genmsg/cmake/../../../lib/genmsg/genmsg_check_deps.py"
