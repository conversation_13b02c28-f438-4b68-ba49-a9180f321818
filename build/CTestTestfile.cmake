# CMake generated Testfile for 
# Source directory: /home/<USER>/workspace/arm_control_ws/src
# Build directory: /home/<USER>/workspace/arm_control_ws/build
# 
# This file includes the relevant testing commands required for 
# testing this directory and lists subdirectories to be tested as well.
subdirs("gtest")
subdirs("pose_detec_hand/src/senseglove/senseglove_launch")
subdirs("pose_detec_hand/src/senseglove/senseglove_shared_resources")
subdirs("pose_detec_hand/src/senseglove/senseglove_finger_distance")
subdirs("pose_detec_hand/src/hand_link")
subdirs("pose_detec_hand/src/hand_link_right")
subdirs("pose_detec_mediapipe/src/pose_mediapipe")
subdirs("pose_detec_openTeleVision")
subdirs("robot_controller")
subdirs("pose_detec_hand/src/senseglove/senseglove_haptics")
subdirs("pose_detec_hand/src/inspire_hand")
subdirs("pose_detec_hand/src/inspire_hand_right")
subdirs("pose_detec_hand/src/imu_interface")
subdirs("pose_detec_hand/src/hardware_interface/senseglove_hardware")
subdirs("pose_detec_hand/src/hardware_interface/senseglove_hardware_builder")
subdirs("pose_detec_hand/src/senseglove/senseglove_description")
subdirs("pose_detec_hand/src/hardware_interface/senseglove_hardware_interface")
