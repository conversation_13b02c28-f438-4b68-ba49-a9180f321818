// Generated by gencpp from file hand_link/set_pos.msg
// DO NOT EDIT!


#ifndef HAND_LINK_MESSAGE_SET_POS_H
#define HAND_LINK_MESSAGE_SET_POS_H

#include <ros/service_traits.h>


#include <hand_link/set_posRequest.h>
#include <hand_link/set_posResponse.h>


namespace hand_link
{

struct set_pos
{

typedef set_posRequest Request;
typedef set_posResponse Response;
Request request;
Response response;

typedef Request RequestType;
typedef Response ResponseType;

}; // struct set_pos
} // namespace hand_link


namespace ros
{
namespace service_traits
{


template<>
struct MD5Sum< ::hand_link::set_pos > {
  static const char* value()
  {
    return "469bce018568d1f214a0c5f75e89bf97";
  }

  static const char* value(const ::hand_link::set_pos&) { return value(); }
};

template<>
struct DataType< ::hand_link::set_pos > {
  static const char* value()
  {
    return "hand_link/set_pos";
  }

  static const char* value(const ::hand_link::set_pos&) { return value(); }
};


// service_traits::MD5Sum< ::hand_link::set_posRequest> should match
// service_traits::MD5Sum< ::hand_link::set_pos >
template<>
struct MD5Sum< ::hand_link::set_posRequest>
{
  static const char* value()
  {
    return MD5Sum< ::hand_link::set_pos >::value();
  }
  static const char* value(const ::hand_link::set_posRequest&)
  {
    return value();
  }
};

// service_traits::DataType< ::hand_link::set_posRequest> should match
// service_traits::DataType< ::hand_link::set_pos >
template<>
struct DataType< ::hand_link::set_posRequest>
{
  static const char* value()
  {
    return DataType< ::hand_link::set_pos >::value();
  }
  static const char* value(const ::hand_link::set_posRequest&)
  {
    return value();
  }
};

// service_traits::MD5Sum< ::hand_link::set_posResponse> should match
// service_traits::MD5Sum< ::hand_link::set_pos >
template<>
struct MD5Sum< ::hand_link::set_posResponse>
{
  static const char* value()
  {
    return MD5Sum< ::hand_link::set_pos >::value();
  }
  static const char* value(const ::hand_link::set_posResponse&)
  {
    return value();
  }
};

// service_traits::DataType< ::hand_link::set_posResponse> should match
// service_traits::DataType< ::hand_link::set_pos >
template<>
struct DataType< ::hand_link::set_posResponse>
{
  static const char* value()
  {
    return DataType< ::hand_link::set_pos >::value();
  }
  static const char* value(const ::hand_link::set_posResponse&)
  {
    return value();
  }
};

} // namespace service_traits
} // namespace ros

#endif // HAND_LINK_MESSAGE_SET_POS_H
