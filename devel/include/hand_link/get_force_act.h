// Generated by gencpp from file hand_link/get_force_act.msg
// DO NOT EDIT!


#ifndef HAND_LINK_MESSAGE_GET_FORCE_ACT_H
#define HAND_LINK_MESSAGE_GET_FORCE_ACT_H

#include <ros/service_traits.h>


#include <hand_link/get_force_actRequest.h>
#include <hand_link/get_force_actResponse.h>


namespace hand_link
{

struct get_force_act
{

typedef get_force_actRequest Request;
typedef get_force_actResponse Response;
Request request;
Response response;

typedef Request RequestType;
typedef Response ResponseType;

}; // struct get_force_act
} // namespace hand_link


namespace ros
{
namespace service_traits
{


template<>
struct MD5Sum< ::hand_link::get_force_act > {
  static const char* value()
  {
    return "70074285dbeda980356fefe582262f12";
  }

  static const char* value(const ::hand_link::get_force_act&) { return value(); }
};

template<>
struct DataType< ::hand_link::get_force_act > {
  static const char* value()
  {
    return "hand_link/get_force_act";
  }

  static const char* value(const ::hand_link::get_force_act&) { return value(); }
};


// service_traits::MD5Sum< ::hand_link::get_force_actRequest> should match
// service_traits::MD5Sum< ::hand_link::get_force_act >
template<>
struct MD5Sum< ::hand_link::get_force_actRequest>
{
  static const char* value()
  {
    return MD5Sum< ::hand_link::get_force_act >::value();
  }
  static const char* value(const ::hand_link::get_force_actRequest&)
  {
    return value();
  }
};

// service_traits::DataType< ::hand_link::get_force_actRequest> should match
// service_traits::DataType< ::hand_link::get_force_act >
template<>
struct DataType< ::hand_link::get_force_actRequest>
{
  static const char* value()
  {
    return DataType< ::hand_link::get_force_act >::value();
  }
  static const char* value(const ::hand_link::get_force_actRequest&)
  {
    return value();
  }
};

// service_traits::MD5Sum< ::hand_link::get_force_actResponse> should match
// service_traits::MD5Sum< ::hand_link::get_force_act >
template<>
struct MD5Sum< ::hand_link::get_force_actResponse>
{
  static const char* value()
  {
    return MD5Sum< ::hand_link::get_force_act >::value();
  }
  static const char* value(const ::hand_link::get_force_actResponse&)
  {
    return value();
  }
};

// service_traits::DataType< ::hand_link::get_force_actResponse> should match
// service_traits::DataType< ::hand_link::get_force_act >
template<>
struct DataType< ::hand_link::get_force_actResponse>
{
  static const char* value()
  {
    return DataType< ::hand_link::get_force_act >::value();
  }
  static const char* value(const ::hand_link::get_force_actResponse&)
  {
    return value();
  }
};

} // namespace service_traits
} // namespace ros

#endif // HAND_LINK_MESSAGE_GET_FORCE_ACT_H
