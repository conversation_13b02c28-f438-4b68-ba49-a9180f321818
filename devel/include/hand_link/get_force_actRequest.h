// Generated by gencpp from file hand_link/get_force_actRequest.msg
// DO NOT EDIT!


#ifndef HAND_LINK_MESSAGE_GET_FORCE_ACTREQUEST_H
#define HAND_LINK_MESSAGE_GET_FORCE_ACTREQUEST_H


#include <string>
#include <vector>
#include <memory>

#include <ros/types.h>
#include <ros/serialization.h>
#include <ros/builtin_message_traits.h>
#include <ros/message_operations.h>


namespace hand_link
{
template <class ContainerAllocator>
struct get_force_actRequest_
{
  typedef get_force_actRequest_<ContainerAllocator> Type;

  get_force_actRequest_()
    {
    }
  get_force_actRequest_(const ContainerAllocator& _alloc)
    {
  (void)_alloc;
    }







  typedef boost::shared_ptr< ::hand_link::get_force_actRequest_<ContainerAllocator> > Ptr;
  typedef boost::shared_ptr< ::hand_link::get_force_actRequest_<ContainerAllocator> const> ConstPtr;

}; // struct get_force_actRequest_

typedef ::hand_link::get_force_actRequest_<std::allocator<void> > get_force_actRequest;

typedef boost::shared_ptr< ::hand_link::get_force_actRequest > get_force_actRequestPtr;
typedef boost::shared_ptr< ::hand_link::get_force_actRequest const> get_force_actRequestConstPtr;

// constants requiring out of line definition



template<typename ContainerAllocator>
std::ostream& operator<<(std::ostream& s, const ::hand_link::get_force_actRequest_<ContainerAllocator> & v)
{
ros::message_operations::Printer< ::hand_link::get_force_actRequest_<ContainerAllocator> >::stream(s, "", v);
return s;
}


} // namespace hand_link

namespace ros
{
namespace message_traits
{





template <class ContainerAllocator>
struct IsMessage< ::hand_link::get_force_actRequest_<ContainerAllocator> >
  : TrueType
  { };

template <class ContainerAllocator>
struct IsMessage< ::hand_link::get_force_actRequest_<ContainerAllocator> const>
  : TrueType
  { };

template <class ContainerAllocator>
struct IsFixedSize< ::hand_link::get_force_actRequest_<ContainerAllocator> >
  : TrueType
  { };

template <class ContainerAllocator>
struct IsFixedSize< ::hand_link::get_force_actRequest_<ContainerAllocator> const>
  : TrueType
  { };

template <class ContainerAllocator>
struct HasHeader< ::hand_link::get_force_actRequest_<ContainerAllocator> >
  : FalseType
  { };

template <class ContainerAllocator>
struct HasHeader< ::hand_link::get_force_actRequest_<ContainerAllocator> const>
  : FalseType
  { };


template<class ContainerAllocator>
struct MD5Sum< ::hand_link::get_force_actRequest_<ContainerAllocator> >
{
  static const char* value()
  {
    return "d41d8cd98f00b204e9800998ecf8427e";
  }

  static const char* value(const ::hand_link::get_force_actRequest_<ContainerAllocator>&) { return value(); }
  static const uint64_t static_value1 = 0xd41d8cd98f00b204ULL;
  static const uint64_t static_value2 = 0xe9800998ecf8427eULL;
};

template<class ContainerAllocator>
struct DataType< ::hand_link::get_force_actRequest_<ContainerAllocator> >
{
  static const char* value()
  {
    return "hand_link/get_force_actRequest";
  }

  static const char* value(const ::hand_link::get_force_actRequest_<ContainerAllocator>&) { return value(); }
};

template<class ContainerAllocator>
struct Definition< ::hand_link::get_force_actRequest_<ContainerAllocator> >
{
  static const char* value()
  {
    return "\n"
;
  }

  static const char* value(const ::hand_link::get_force_actRequest_<ContainerAllocator>&) { return value(); }
};

} // namespace message_traits
} // namespace ros

namespace ros
{
namespace serialization
{

  template<class ContainerAllocator> struct Serializer< ::hand_link::get_force_actRequest_<ContainerAllocator> >
  {
    template<typename Stream, typename T> inline static void allInOne(Stream&, T)
    {}

    ROS_DECLARE_ALLINONE_SERIALIZER
  }; // struct get_force_actRequest_

} // namespace serialization
} // namespace ros

namespace ros
{
namespace message_operations
{

template<class ContainerAllocator>
struct Printer< ::hand_link::get_force_actRequest_<ContainerAllocator> >
{
  template<typename Stream> static void stream(Stream&, const std::string&, const ::hand_link::get_force_actRequest_<ContainerAllocator>&)
  {}
};

} // namespace message_operations
} // namespace ros

#endif // HAND_LINK_MESSAGE_GET_FORCE_ACTREQUEST_H
