// Generated by gencpp from file hand_link/set_posRequest.msg
// DO NOT EDIT!


#ifndef HAND_LINK_MESSAGE_SET_POSREQUEST_H
#define HAND_LINK_MESSAGE_SET_POSREQUEST_H


#include <string>
#include <vector>
#include <memory>

#include <ros/types.h>
#include <ros/serialization.h>
#include <ros/builtin_message_traits.h>
#include <ros/message_operations.h>


namespace hand_link
{
template <class ContainerAllocator>
struct set_posRequest_
{
  typedef set_posRequest_<ContainerAllocator> Type;

  set_posRequest_()
    : pos0(0)
    , pos1(0)
    , pos2(0)
    , pos3(0)
    , pos4(0)
    , pos5(0)  {
    }
  set_posRequest_(const ContainerAllocator& _alloc)
    : pos0(0)
    , pos1(0)
    , pos2(0)
    , pos3(0)
    , pos4(0)
    , pos5(0)  {
  (void)_alloc;
    }



   typedef int32_t _pos0_type;
  _pos0_type pos0;

   typedef int32_t _pos1_type;
  _pos1_type pos1;

   typedef int32_t _pos2_type;
  _pos2_type pos2;

   typedef int32_t _pos3_type;
  _pos3_type pos3;

   typedef int32_t _pos4_type;
  _pos4_type pos4;

   typedef int32_t _pos5_type;
  _pos5_type pos5;





  typedef boost::shared_ptr< ::hand_link::set_posRequest_<ContainerAllocator> > Ptr;
  typedef boost::shared_ptr< ::hand_link::set_posRequest_<ContainerAllocator> const> ConstPtr;

}; // struct set_posRequest_

typedef ::hand_link::set_posRequest_<std::allocator<void> > set_posRequest;

typedef boost::shared_ptr< ::hand_link::set_posRequest > set_posRequestPtr;
typedef boost::shared_ptr< ::hand_link::set_posRequest const> set_posRequestConstPtr;

// constants requiring out of line definition



template<typename ContainerAllocator>
std::ostream& operator<<(std::ostream& s, const ::hand_link::set_posRequest_<ContainerAllocator> & v)
{
ros::message_operations::Printer< ::hand_link::set_posRequest_<ContainerAllocator> >::stream(s, "", v);
return s;
}


template<typename ContainerAllocator1, typename ContainerAllocator2>
bool operator==(const ::hand_link::set_posRequest_<ContainerAllocator1> & lhs, const ::hand_link::set_posRequest_<ContainerAllocator2> & rhs)
{
  return lhs.pos0 == rhs.pos0 &&
    lhs.pos1 == rhs.pos1 &&
    lhs.pos2 == rhs.pos2 &&
    lhs.pos3 == rhs.pos3 &&
    lhs.pos4 == rhs.pos4 &&
    lhs.pos5 == rhs.pos5;
}

template<typename ContainerAllocator1, typename ContainerAllocator2>
bool operator!=(const ::hand_link::set_posRequest_<ContainerAllocator1> & lhs, const ::hand_link::set_posRequest_<ContainerAllocator2> & rhs)
{
  return !(lhs == rhs);
}


} // namespace hand_link

namespace ros
{
namespace message_traits
{





template <class ContainerAllocator>
struct IsMessage< ::hand_link::set_posRequest_<ContainerAllocator> >
  : TrueType
  { };

template <class ContainerAllocator>
struct IsMessage< ::hand_link::set_posRequest_<ContainerAllocator> const>
  : TrueType
  { };

template <class ContainerAllocator>
struct IsFixedSize< ::hand_link::set_posRequest_<ContainerAllocator> >
  : TrueType
  { };

template <class ContainerAllocator>
struct IsFixedSize< ::hand_link::set_posRequest_<ContainerAllocator> const>
  : TrueType
  { };

template <class ContainerAllocator>
struct HasHeader< ::hand_link::set_posRequest_<ContainerAllocator> >
  : FalseType
  { };

template <class ContainerAllocator>
struct HasHeader< ::hand_link::set_posRequest_<ContainerAllocator> const>
  : FalseType
  { };


template<class ContainerAllocator>
struct MD5Sum< ::hand_link::set_posRequest_<ContainerAllocator> >
{
  static const char* value()
  {
    return "44ecda5531a5154559fe37419faa32a4";
  }

  static const char* value(const ::hand_link::set_posRequest_<ContainerAllocator>&) { return value(); }
  static const uint64_t static_value1 = 0x44ecda5531a51545ULL;
  static const uint64_t static_value2 = 0x59fe37419faa32a4ULL;
};

template<class ContainerAllocator>
struct DataType< ::hand_link::set_posRequest_<ContainerAllocator> >
{
  static const char* value()
  {
    return "hand_link/set_posRequest";
  }

  static const char* value(const ::hand_link::set_posRequest_<ContainerAllocator>&) { return value(); }
};

template<class ContainerAllocator>
struct Definition< ::hand_link::set_posRequest_<ContainerAllocator> >
{
  static const char* value()
  {
    return "int32 pos0\n"
"int32 pos1\n"
"int32 pos2\n"
"int32 pos3\n"
"int32 pos4\n"
"int32 pos5\n"
;
  }

  static const char* value(const ::hand_link::set_posRequest_<ContainerAllocator>&) { return value(); }
};

} // namespace message_traits
} // namespace ros

namespace ros
{
namespace serialization
{

  template<class ContainerAllocator> struct Serializer< ::hand_link::set_posRequest_<ContainerAllocator> >
  {
    template<typename Stream, typename T> inline static void allInOne(Stream& stream, T m)
    {
      stream.next(m.pos0);
      stream.next(m.pos1);
      stream.next(m.pos2);
      stream.next(m.pos3);
      stream.next(m.pos4);
      stream.next(m.pos5);
    }

    ROS_DECLARE_ALLINONE_SERIALIZER
  }; // struct set_posRequest_

} // namespace serialization
} // namespace ros

namespace ros
{
namespace message_operations
{

template<class ContainerAllocator>
struct Printer< ::hand_link::set_posRequest_<ContainerAllocator> >
{
  template<typename Stream> static void stream(Stream& s, const std::string& indent, const ::hand_link::set_posRequest_<ContainerAllocator>& v)
  {
    if (false || !indent.empty())
      s << std::endl;
    s << indent << "pos0: ";
    Printer<int32_t>::stream(s, indent + "  ", v.pos0);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "pos1: ";
    Printer<int32_t>::stream(s, indent + "  ", v.pos1);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "pos2: ";
    Printer<int32_t>::stream(s, indent + "  ", v.pos2);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "pos3: ";
    Printer<int32_t>::stream(s, indent + "  ", v.pos3);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "pos4: ";
    Printer<int32_t>::stream(s, indent + "  ", v.pos4);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "pos5: ";
    Printer<int32_t>::stream(s, indent + "  ", v.pos5);
  }
};

} // namespace message_operations
} // namespace ros

#endif // HAND_LINK_MESSAGE_SET_POSREQUEST_H
