// Generated by gencpp from file hand_link/set_posResponse.msg
// DO NOT EDIT!


#ifndef HAND_LINK_MESSAGE_SET_POSRESPONSE_H
#define HAND_LINK_MESSAGE_SET_POSRESPONSE_H


#include <string>
#include <vector>
#include <memory>

#include <ros/types.h>
#include <ros/serialization.h>
#include <ros/builtin_message_traits.h>
#include <ros/message_operations.h>


namespace hand_link
{
template <class ContainerAllocator>
struct set_posResponse_
{
  typedef set_posResponse_<ContainerAllocator> Type;

  set_posResponse_()
    : pos_accepted(false)  {
    }
  set_posResponse_(const ContainerAllocator& _alloc)
    : pos_accepted(false)  {
  (void)_alloc;
    }



   typedef uint8_t _pos_accepted_type;
  _pos_accepted_type pos_accepted;





  typedef boost::shared_ptr< ::hand_link::set_posResponse_<ContainerAllocator> > Ptr;
  typedef boost::shared_ptr< ::hand_link::set_posResponse_<ContainerAllocator> const> ConstPtr;

}; // struct set_posResponse_

typedef ::hand_link::set_posResponse_<std::allocator<void> > set_posResponse;

typedef boost::shared_ptr< ::hand_link::set_posResponse > set_posResponsePtr;
typedef boost::shared_ptr< ::hand_link::set_posResponse const> set_posResponseConstPtr;

// constants requiring out of line definition



template<typename ContainerAllocator>
std::ostream& operator<<(std::ostream& s, const ::hand_link::set_posResponse_<ContainerAllocator> & v)
{
ros::message_operations::Printer< ::hand_link::set_posResponse_<ContainerAllocator> >::stream(s, "", v);
return s;
}


template<typename ContainerAllocator1, typename ContainerAllocator2>
bool operator==(const ::hand_link::set_posResponse_<ContainerAllocator1> & lhs, const ::hand_link::set_posResponse_<ContainerAllocator2> & rhs)
{
  return lhs.pos_accepted == rhs.pos_accepted;
}

template<typename ContainerAllocator1, typename ContainerAllocator2>
bool operator!=(const ::hand_link::set_posResponse_<ContainerAllocator1> & lhs, const ::hand_link::set_posResponse_<ContainerAllocator2> & rhs)
{
  return !(lhs == rhs);
}


} // namespace hand_link

namespace ros
{
namespace message_traits
{





template <class ContainerAllocator>
struct IsMessage< ::hand_link::set_posResponse_<ContainerAllocator> >
  : TrueType
  { };

template <class ContainerAllocator>
struct IsMessage< ::hand_link::set_posResponse_<ContainerAllocator> const>
  : TrueType
  { };

template <class ContainerAllocator>
struct IsFixedSize< ::hand_link::set_posResponse_<ContainerAllocator> >
  : TrueType
  { };

template <class ContainerAllocator>
struct IsFixedSize< ::hand_link::set_posResponse_<ContainerAllocator> const>
  : TrueType
  { };

template <class ContainerAllocator>
struct HasHeader< ::hand_link::set_posResponse_<ContainerAllocator> >
  : FalseType
  { };

template <class ContainerAllocator>
struct HasHeader< ::hand_link::set_posResponse_<ContainerAllocator> const>
  : FalseType
  { };


template<class ContainerAllocator>
struct MD5Sum< ::hand_link::set_posResponse_<ContainerAllocator> >
{
  static const char* value()
  {
    return "642adf9364b32587cec215516fb006b2";
  }

  static const char* value(const ::hand_link::set_posResponse_<ContainerAllocator>&) { return value(); }
  static const uint64_t static_value1 = 0x642adf9364b32587ULL;
  static const uint64_t static_value2 = 0xcec215516fb006b2ULL;
};

template<class ContainerAllocator>
struct DataType< ::hand_link::set_posResponse_<ContainerAllocator> >
{
  static const char* value()
  {
    return "hand_link/set_posResponse";
  }

  static const char* value(const ::hand_link::set_posResponse_<ContainerAllocator>&) { return value(); }
};

template<class ContainerAllocator>
struct Definition< ::hand_link::set_posResponse_<ContainerAllocator> >
{
  static const char* value()
  {
    return "bool pos_accepted\n"
;
  }

  static const char* value(const ::hand_link::set_posResponse_<ContainerAllocator>&) { return value(); }
};

} // namespace message_traits
} // namespace ros

namespace ros
{
namespace serialization
{

  template<class ContainerAllocator> struct Serializer< ::hand_link::set_posResponse_<ContainerAllocator> >
  {
    template<typename Stream, typename T> inline static void allInOne(Stream& stream, T m)
    {
      stream.next(m.pos_accepted);
    }

    ROS_DECLARE_ALLINONE_SERIALIZER
  }; // struct set_posResponse_

} // namespace serialization
} // namespace ros

namespace ros
{
namespace message_operations
{

template<class ContainerAllocator>
struct Printer< ::hand_link::set_posResponse_<ContainerAllocator> >
{
  template<typename Stream> static void stream(Stream& s, const std::string& indent, const ::hand_link::set_posResponse_<ContainerAllocator>& v)
  {
    if (false || !indent.empty())
      s << std::endl;
    s << indent << "pos_accepted: ";
    Printer<uint8_t>::stream(s, indent + "  ", v.pos_accepted);
  }
};

} // namespace message_operations
} // namespace ros

#endif // HAND_LINK_MESSAGE_SET_POSRESPONSE_H
