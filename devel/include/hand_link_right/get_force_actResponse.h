// Generated by gencpp from file hand_link_right/get_force_actResponse.msg
// DO NOT EDIT!


#ifndef HAND_LINK_RIGHT_MESSAGE_GET_FORCE_ACTRESPONSE_H
#define HAND_LINK_RIGHT_MESSAGE_GET_FORCE_ACTRESPONSE_H


#include <string>
#include <vector>
#include <memory>

#include <ros/types.h>
#include <ros/serialization.h>
#include <ros/builtin_message_traits.h>
#include <ros/message_operations.h>


namespace hand_link_right
{
template <class ContainerAllocator>
struct get_force_actResponse_
{
  typedef get_force_actResponse_<ContainerAllocator> Type;

  get_force_actResponse_()
    : curforce()  {
      curforce.assign(0.0);
  }
  get_force_actResponse_(const ContainerAllocator& _alloc)
    : curforce()  {
  (void)_alloc;
      curforce.assign(0.0);
  }



   typedef boost::array<float, 6>  _curforce_type;
  _curforce_type curforce;





  typedef boost::shared_ptr< ::hand_link_right::get_force_actResponse_<ContainerAllocator> > Ptr;
  typedef boost::shared_ptr< ::hand_link_right::get_force_actResponse_<ContainerAllocator> const> ConstPtr;

}; // struct get_force_actResponse_

typedef ::hand_link_right::get_force_actResponse_<std::allocator<void> > get_force_actResponse;

typedef boost::shared_ptr< ::hand_link_right::get_force_actResponse > get_force_actResponsePtr;
typedef boost::shared_ptr< ::hand_link_right::get_force_actResponse const> get_force_actResponseConstPtr;

// constants requiring out of line definition



template<typename ContainerAllocator>
std::ostream& operator<<(std::ostream& s, const ::hand_link_right::get_force_actResponse_<ContainerAllocator> & v)
{
ros::message_operations::Printer< ::hand_link_right::get_force_actResponse_<ContainerAllocator> >::stream(s, "", v);
return s;
}


template<typename ContainerAllocator1, typename ContainerAllocator2>
bool operator==(const ::hand_link_right::get_force_actResponse_<ContainerAllocator1> & lhs, const ::hand_link_right::get_force_actResponse_<ContainerAllocator2> & rhs)
{
  return lhs.curforce == rhs.curforce;
}

template<typename ContainerAllocator1, typename ContainerAllocator2>
bool operator!=(const ::hand_link_right::get_force_actResponse_<ContainerAllocator1> & lhs, const ::hand_link_right::get_force_actResponse_<ContainerAllocator2> & rhs)
{
  return !(lhs == rhs);
}


} // namespace hand_link_right

namespace ros
{
namespace message_traits
{





template <class ContainerAllocator>
struct IsMessage< ::hand_link_right::get_force_actResponse_<ContainerAllocator> >
  : TrueType
  { };

template <class ContainerAllocator>
struct IsMessage< ::hand_link_right::get_force_actResponse_<ContainerAllocator> const>
  : TrueType
  { };

template <class ContainerAllocator>
struct IsFixedSize< ::hand_link_right::get_force_actResponse_<ContainerAllocator> >
  : TrueType
  { };

template <class ContainerAllocator>
struct IsFixedSize< ::hand_link_right::get_force_actResponse_<ContainerAllocator> const>
  : TrueType
  { };

template <class ContainerAllocator>
struct HasHeader< ::hand_link_right::get_force_actResponse_<ContainerAllocator> >
  : FalseType
  { };

template <class ContainerAllocator>
struct HasHeader< ::hand_link_right::get_force_actResponse_<ContainerAllocator> const>
  : FalseType
  { };


template<class ContainerAllocator>
struct MD5Sum< ::hand_link_right::get_force_actResponse_<ContainerAllocator> >
{
  static const char* value()
  {
    return "70074285dbeda980356fefe582262f12";
  }

  static const char* value(const ::hand_link_right::get_force_actResponse_<ContainerAllocator>&) { return value(); }
  static const uint64_t static_value1 = 0x70074285dbeda980ULL;
  static const uint64_t static_value2 = 0x356fefe582262f12ULL;
};

template<class ContainerAllocator>
struct DataType< ::hand_link_right::get_force_actResponse_<ContainerAllocator> >
{
  static const char* value()
  {
    return "hand_link_right/get_force_actResponse";
  }

  static const char* value(const ::hand_link_right::get_force_actResponse_<ContainerAllocator>&) { return value(); }
};

template<class ContainerAllocator>
struct Definition< ::hand_link_right::get_force_actResponse_<ContainerAllocator> >
{
  static const char* value()
  {
    return "float32[6] curforce\n"
;
  }

  static const char* value(const ::hand_link_right::get_force_actResponse_<ContainerAllocator>&) { return value(); }
};

} // namespace message_traits
} // namespace ros

namespace ros
{
namespace serialization
{

  template<class ContainerAllocator> struct Serializer< ::hand_link_right::get_force_actResponse_<ContainerAllocator> >
  {
    template<typename Stream, typename T> inline static void allInOne(Stream& stream, T m)
    {
      stream.next(m.curforce);
    }

    ROS_DECLARE_ALLINONE_SERIALIZER
  }; // struct get_force_actResponse_

} // namespace serialization
} // namespace ros

namespace ros
{
namespace message_operations
{

template<class ContainerAllocator>
struct Printer< ::hand_link_right::get_force_actResponse_<ContainerAllocator> >
{
  template<typename Stream> static void stream(Stream& s, const std::string& indent, const ::hand_link_right::get_force_actResponse_<ContainerAllocator>& v)
  {
    if (false || !indent.empty())
      s << std::endl;
    s << indent << "curforce: ";
    if (v.curforce.empty() || true)
      s << "[";
    for (size_t i = 0; i < v.curforce.size(); ++i)
    {
      if (true && i > 0)
        s << ", ";
      else if (!true)
        s << std::endl << indent << "  -";
      Printer<float>::stream(s, true ? std::string() : indent + "    ", v.curforce[i]);
    }
    if (v.curforce.empty() || true)
      s << "]";
  }
};

} // namespace message_operations
} // namespace ros

#endif // HAND_LINK_RIGHT_MESSAGE_GET_FORCE_ACTRESPONSE_H
