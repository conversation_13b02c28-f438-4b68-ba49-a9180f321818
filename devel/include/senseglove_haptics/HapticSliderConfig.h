//#line 2 "/opt/ros/noetic/share/dynamic_reconfigure/cmake/../templates/ConfigType.h.template"
// *********************************************************
//
// File autogenerated for the senseglove_haptics package
// by the dynamic_reconfigure package.
// Please do not edit.
//
// ********************************************************/

#ifndef __senseglove_haptics__HAPTICSLIDERCONFIG_H__
#define __senseglove_haptics__HAPTICSLIDERCONFIG_H__

#if __cplusplus >= 201103L
#define DYNAMIC_RECONFIGURE_FINAL final
#else
#define DYNAMIC_RECONFIGURE_FINAL
#endif

#include <dynamic_reconfigure/config_tools.h>
#include <limits>
#include <ros/node_handle.h>
#include <dynamic_reconfigure/ConfigDescription.h>
#include <dynamic_reconfigure/ParamDescription.h>
#include <dynamic_reconfigure/Group.h>
#include <dynamic_reconfigure/config_init_mutex.h>
#include <boost/any.hpp>

namespace senseglove_haptics
{
  class HapticSliderConfigStatics;

  class HapticSliderConfig
  {
  public:
    class AbstractParamDescription : public dynamic_reconfigure::ParamDescription
    {
    public:
      AbstractParamDescription(std::string n, std::string t, uint32_t l,
          std::string d, std::string e)
      {
        name = n;
        type = t;
        level = l;
        description = d;
        edit_method = e;
      }
      virtual ~AbstractParamDescription() = default;

      virtual void clamp(HapticSliderConfig &config, const HapticSliderConfig &max, const HapticSliderConfig &min) const = 0;
      virtual void calcLevel(uint32_t &level, const HapticSliderConfig &config1, const HapticSliderConfig &config2) const = 0;
      virtual void fromServer(const ros::NodeHandle &nh, HapticSliderConfig &config) const = 0;
      virtual void toServer(const ros::NodeHandle &nh, const HapticSliderConfig &config) const = 0;
      virtual bool fromMessage(const dynamic_reconfigure::Config &msg, HapticSliderConfig &config) const = 0;
      virtual void toMessage(dynamic_reconfigure::Config &msg, const HapticSliderConfig &config) const = 0;
      virtual void getValue(const HapticSliderConfig &config, boost::any &val) const = 0;
    };

    typedef boost::shared_ptr<AbstractParamDescription> AbstractParamDescriptionPtr;
    typedef boost::shared_ptr<const AbstractParamDescription> AbstractParamDescriptionConstPtr;

    // Final keyword added to class because it has virtual methods and inherits
    // from a class with a non-virtual destructor.
    template <class T>
    class ParamDescription DYNAMIC_RECONFIGURE_FINAL : public AbstractParamDescription
    {
    public:
      ParamDescription(std::string a_name, std::string a_type, uint32_t a_level,
          std::string a_description, std::string a_edit_method, T HapticSliderConfig::* a_f) :
        AbstractParamDescription(a_name, a_type, a_level, a_description, a_edit_method),
        field(a_f)
      {}

      T HapticSliderConfig::* field;

      virtual void clamp(HapticSliderConfig &config, const HapticSliderConfig &max, const HapticSliderConfig &min) const override
      {
        if (config.*field > max.*field)
          config.*field = max.*field;

        if (config.*field < min.*field)
          config.*field = min.*field;
      }

      virtual void calcLevel(uint32_t &comb_level, const HapticSliderConfig &config1, const HapticSliderConfig &config2) const override
      {
        if (config1.*field != config2.*field)
          comb_level |= level;
      }

      virtual void fromServer(const ros::NodeHandle &nh, HapticSliderConfig &config) const override
      {
        nh.getParam(name, config.*field);
      }

      virtual void toServer(const ros::NodeHandle &nh, const HapticSliderConfig &config) const override
      {
        nh.setParam(name, config.*field);
      }

      virtual bool fromMessage(const dynamic_reconfigure::Config &msg, HapticSliderConfig &config) const override
      {
        return dynamic_reconfigure::ConfigTools::getParameter(msg, name, config.*field);
      }

      virtual void toMessage(dynamic_reconfigure::Config &msg, const HapticSliderConfig &config) const override
      {
        dynamic_reconfigure::ConfigTools::appendParameter(msg, name, config.*field);
      }

      virtual void getValue(const HapticSliderConfig &config, boost::any &val) const override
      {
        val = config.*field;
      }
    };

    class AbstractGroupDescription : public dynamic_reconfigure::Group
    {
      public:
      AbstractGroupDescription(std::string n, std::string t, int p, int i, bool s)
      {
        name = n;
        type = t;
        parent = p;
        state = s;
        id = i;
      }

      virtual ~AbstractGroupDescription() = default;

      std::vector<AbstractParamDescriptionConstPtr> abstract_parameters;
      bool state;

      virtual void toMessage(dynamic_reconfigure::Config &msg, const boost::any &config) const = 0;
      virtual bool fromMessage(const dynamic_reconfigure::Config &msg, boost::any &config) const =0;
      virtual void updateParams(boost::any &cfg, HapticSliderConfig &top) const= 0;
      virtual void setInitialState(boost::any &cfg) const = 0;


      void convertParams()
      {
        for(std::vector<AbstractParamDescriptionConstPtr>::const_iterator i = abstract_parameters.begin(); i != abstract_parameters.end(); ++i)
        {
          parameters.push_back(dynamic_reconfigure::ParamDescription(**i));
        }
      }
    };

    typedef boost::shared_ptr<AbstractGroupDescription> AbstractGroupDescriptionPtr;
    typedef boost::shared_ptr<const AbstractGroupDescription> AbstractGroupDescriptionConstPtr;

    // Final keyword added to class because it has virtual methods and inherits
    // from a class with a non-virtual destructor.
    template<class T, class PT>
    class GroupDescription DYNAMIC_RECONFIGURE_FINAL : public AbstractGroupDescription
    {
    public:
      GroupDescription(std::string a_name, std::string a_type, int a_parent, int a_id, bool a_s, T PT::* a_f) : AbstractGroupDescription(a_name, a_type, a_parent, a_id, a_s), field(a_f)
      {
      }

      GroupDescription(const GroupDescription<T, PT>& g): AbstractGroupDescription(g.name, g.type, g.parent, g.id, g.state), field(g.field), groups(g.groups)
      {
        parameters = g.parameters;
        abstract_parameters = g.abstract_parameters;
      }

      virtual bool fromMessage(const dynamic_reconfigure::Config &msg, boost::any &cfg) const override
      {
        PT* config = boost::any_cast<PT*>(cfg);
        if(!dynamic_reconfigure::ConfigTools::getGroupState(msg, name, (*config).*field))
          return false;

        for(std::vector<AbstractGroupDescriptionConstPtr>::const_iterator i = groups.begin(); i != groups.end(); ++i)
        {
          boost::any n = &((*config).*field);
          if(!(*i)->fromMessage(msg, n))
            return false;
        }

        return true;
      }

      virtual void setInitialState(boost::any &cfg) const override
      {
        PT* config = boost::any_cast<PT*>(cfg);
        T* group = &((*config).*field);
        group->state = state;

        for(std::vector<AbstractGroupDescriptionConstPtr>::const_iterator i = groups.begin(); i != groups.end(); ++i)
        {
          boost::any n = boost::any(&((*config).*field));
          (*i)->setInitialState(n);
        }

      }

      virtual void updateParams(boost::any &cfg, HapticSliderConfig &top) const override
      {
        PT* config = boost::any_cast<PT*>(cfg);

        T* f = &((*config).*field);
        f->setParams(top, abstract_parameters);

        for(std::vector<AbstractGroupDescriptionConstPtr>::const_iterator i = groups.begin(); i != groups.end(); ++i)
        {
          boost::any n = &((*config).*field);
          (*i)->updateParams(n, top);
        }
      }

      virtual void toMessage(dynamic_reconfigure::Config &msg, const boost::any &cfg) const override
      {
        const PT config = boost::any_cast<PT>(cfg);
        dynamic_reconfigure::ConfigTools::appendGroup<T>(msg, name, id, parent, config.*field);

        for(std::vector<AbstractGroupDescriptionConstPtr>::const_iterator i = groups.begin(); i != groups.end(); ++i)
        {
          (*i)->toMessage(msg, config.*field);
        }
      }

      T PT::* field;
      std::vector<HapticSliderConfig::AbstractGroupDescriptionConstPtr> groups;
    };

class DEFAULT
{
  public:
    DEFAULT()
    {
      state = true;
      name = "Default";
    }

    void setParams(HapticSliderConfig &config, const std::vector<AbstractParamDescriptionConstPtr> params)
    {
      for (std::vector<AbstractParamDescriptionConstPtr>::const_iterator _i = params.begin(); _i != params.end(); ++_i)
      {
        boost::any val;
        (*_i)->getValue(config, val);

        if("thumb_ffb"==(*_i)->name){thumb_ffb = boost::any_cast<double>(val);}
        if("index_ffb"==(*_i)->name){index_ffb = boost::any_cast<double>(val);}
        if("middle_ffb"==(*_i)->name){middle_ffb = boost::any_cast<double>(val);}
        if("ring_ffb"==(*_i)->name){ring_ffb = boost::any_cast<double>(val);}
        if("pinky_ffb"==(*_i)->name){pinky_ffb = boost::any_cast<double>(val);}
        if("thumb_buzz"==(*_i)->name){thumb_buzz = boost::any_cast<double>(val);}
        if("index_buzz"==(*_i)->name){index_buzz = boost::any_cast<double>(val);}
        if("middle_buzz"==(*_i)->name){middle_buzz = boost::any_cast<double>(val);}
        if("ring_buzz"==(*_i)->name){ring_buzz = boost::any_cast<double>(val);}
        if("pinky_buzz"==(*_i)->name){pinky_buzz = boost::any_cast<double>(val);}
        if("thumper_buzz"==(*_i)->name){thumper_buzz = boost::any_cast<double>(val);}
        if("palm_index_buzz"==(*_i)->name){palm_index_buzz = boost::any_cast<double>(val);}
        if("palm_pinky_buzz"==(*_i)->name){palm_pinky_buzz = boost::any_cast<double>(val);}
        if("palm_strap"==(*_i)->name){palm_strap = boost::any_cast<double>(val);}
        if("reset"==(*_i)->name){reset = boost::any_cast<bool>(val);}
      }
    }

    double thumb_ffb;
double index_ffb;
double middle_ffb;
double ring_ffb;
double pinky_ffb;
double thumb_buzz;
double index_buzz;
double middle_buzz;
double ring_buzz;
double pinky_buzz;
double thumper_buzz;
double palm_index_buzz;
double palm_pinky_buzz;
double palm_strap;
bool reset;

    bool state;
    std::string name;

    
}groups;



//#line 292 "/opt/ros/noetic/lib/python3/dist-packages/dynamic_reconfigure/parameter_generator_catkin.py"
      double thumb_ffb;
//#line 292 "/opt/ros/noetic/lib/python3/dist-packages/dynamic_reconfigure/parameter_generator_catkin.py"
      double index_ffb;
//#line 292 "/opt/ros/noetic/lib/python3/dist-packages/dynamic_reconfigure/parameter_generator_catkin.py"
      double middle_ffb;
//#line 292 "/opt/ros/noetic/lib/python3/dist-packages/dynamic_reconfigure/parameter_generator_catkin.py"
      double ring_ffb;
//#line 292 "/opt/ros/noetic/lib/python3/dist-packages/dynamic_reconfigure/parameter_generator_catkin.py"
      double pinky_ffb;
//#line 292 "/opt/ros/noetic/lib/python3/dist-packages/dynamic_reconfigure/parameter_generator_catkin.py"
      double thumb_buzz;
//#line 292 "/opt/ros/noetic/lib/python3/dist-packages/dynamic_reconfigure/parameter_generator_catkin.py"
      double index_buzz;
//#line 292 "/opt/ros/noetic/lib/python3/dist-packages/dynamic_reconfigure/parameter_generator_catkin.py"
      double middle_buzz;
//#line 292 "/opt/ros/noetic/lib/python3/dist-packages/dynamic_reconfigure/parameter_generator_catkin.py"
      double ring_buzz;
//#line 292 "/opt/ros/noetic/lib/python3/dist-packages/dynamic_reconfigure/parameter_generator_catkin.py"
      double pinky_buzz;
//#line 292 "/opt/ros/noetic/lib/python3/dist-packages/dynamic_reconfigure/parameter_generator_catkin.py"
      double thumper_buzz;
//#line 292 "/opt/ros/noetic/lib/python3/dist-packages/dynamic_reconfigure/parameter_generator_catkin.py"
      double palm_index_buzz;
//#line 292 "/opt/ros/noetic/lib/python3/dist-packages/dynamic_reconfigure/parameter_generator_catkin.py"
      double palm_pinky_buzz;
//#line 292 "/opt/ros/noetic/lib/python3/dist-packages/dynamic_reconfigure/parameter_generator_catkin.py"
      double palm_strap;
//#line 292 "/opt/ros/noetic/lib/python3/dist-packages/dynamic_reconfigure/parameter_generator_catkin.py"
      bool reset;
//#line 231 "/opt/ros/noetic/share/dynamic_reconfigure/cmake/../templates/ConfigType.h.template"

    bool __fromMessage__(dynamic_reconfigure::Config &msg)
    {
      const std::vector<AbstractParamDescriptionConstPtr> &__param_descriptions__ = __getParamDescriptions__();
      const std::vector<AbstractGroupDescriptionConstPtr> &__group_descriptions__ = __getGroupDescriptions__();

      int count = 0;
      for (std::vector<AbstractParamDescriptionConstPtr>::const_iterator i = __param_descriptions__.begin(); i != __param_descriptions__.end(); ++i)
        if ((*i)->fromMessage(msg, *this))
          count++;

      for (std::vector<AbstractGroupDescriptionConstPtr>::const_iterator i = __group_descriptions__.begin(); i != __group_descriptions__.end(); i ++)
      {
        if ((*i)->id == 0)
        {
          boost::any n = boost::any(this);
          (*i)->updateParams(n, *this);
          (*i)->fromMessage(msg, n);
        }
      }

      if (count != dynamic_reconfigure::ConfigTools::size(msg))
      {
        ROS_ERROR("HapticSliderConfig::__fromMessage__ called with an unexpected parameter.");
        ROS_ERROR("Booleans:");
        for (size_t i = 0; i < msg.bools.size(); i++)
          ROS_ERROR("  %s", msg.bools[i].name.c_str());
        ROS_ERROR("Integers:");
        for (size_t i = 0; i < msg.ints.size(); i++)
          ROS_ERROR("  %s", msg.ints[i].name.c_str());
        ROS_ERROR("Doubles:");
        for (size_t i = 0; i < msg.doubles.size(); i++)
          ROS_ERROR("  %s", msg.doubles[i].name.c_str());
        ROS_ERROR("Strings:");
        for (size_t i = 0; i < msg.strs.size(); i++)
          ROS_ERROR("  %s", msg.strs[i].name.c_str());
        // @todo Check that there are no duplicates. Make this error more
        // explicit.
        return false;
      }
      return true;
    }

    // This version of __toMessage__ is used during initialization of
    // statics when __getParamDescriptions__ can't be called yet.
    void __toMessage__(dynamic_reconfigure::Config &msg, const std::vector<AbstractParamDescriptionConstPtr> &__param_descriptions__, const std::vector<AbstractGroupDescriptionConstPtr> &__group_descriptions__) const
    {
      dynamic_reconfigure::ConfigTools::clear(msg);
      for (std::vector<AbstractParamDescriptionConstPtr>::const_iterator i = __param_descriptions__.begin(); i != __param_descriptions__.end(); ++i)
        (*i)->toMessage(msg, *this);

      for (std::vector<AbstractGroupDescriptionConstPtr>::const_iterator i = __group_descriptions__.begin(); i != __group_descriptions__.end(); ++i)
      {
        if((*i)->id == 0)
        {
          (*i)->toMessage(msg, *this);
        }
      }
    }

    void __toMessage__(dynamic_reconfigure::Config &msg) const
    {
      const std::vector<AbstractParamDescriptionConstPtr> &__param_descriptions__ = __getParamDescriptions__();
      const std::vector<AbstractGroupDescriptionConstPtr> &__group_descriptions__ = __getGroupDescriptions__();
      __toMessage__(msg, __param_descriptions__, __group_descriptions__);
    }

    void __toServer__(const ros::NodeHandle &nh) const
    {
      const std::vector<AbstractParamDescriptionConstPtr> &__param_descriptions__ = __getParamDescriptions__();
      for (std::vector<AbstractParamDescriptionConstPtr>::const_iterator i = __param_descriptions__.begin(); i != __param_descriptions__.end(); ++i)
        (*i)->toServer(nh, *this);
    }

    void __fromServer__(const ros::NodeHandle &nh)
    {
      static bool setup=false;

      const std::vector<AbstractParamDescriptionConstPtr> &__param_descriptions__ = __getParamDescriptions__();
      for (std::vector<AbstractParamDescriptionConstPtr>::const_iterator i = __param_descriptions__.begin(); i != __param_descriptions__.end(); ++i)
        (*i)->fromServer(nh, *this);

      const std::vector<AbstractGroupDescriptionConstPtr> &__group_descriptions__ = __getGroupDescriptions__();
      for (std::vector<AbstractGroupDescriptionConstPtr>::const_iterator i = __group_descriptions__.begin(); i != __group_descriptions__.end(); i++){
        if (!setup && (*i)->id == 0) {
          setup = true;
          boost::any n = boost::any(this);
          (*i)->setInitialState(n);
        }
      }
    }

    void __clamp__()
    {
      const std::vector<AbstractParamDescriptionConstPtr> &__param_descriptions__ = __getParamDescriptions__();
      const HapticSliderConfig &__max__ = __getMax__();
      const HapticSliderConfig &__min__ = __getMin__();
      for (std::vector<AbstractParamDescriptionConstPtr>::const_iterator i = __param_descriptions__.begin(); i != __param_descriptions__.end(); ++i)
        (*i)->clamp(*this, __max__, __min__);
    }

    uint32_t __level__(const HapticSliderConfig &config) const
    {
      const std::vector<AbstractParamDescriptionConstPtr> &__param_descriptions__ = __getParamDescriptions__();
      uint32_t level = 0;
      for (std::vector<AbstractParamDescriptionConstPtr>::const_iterator i = __param_descriptions__.begin(); i != __param_descriptions__.end(); ++i)
        (*i)->calcLevel(level, config, *this);
      return level;
    }

    static const dynamic_reconfigure::ConfigDescription &__getDescriptionMessage__();
    static const HapticSliderConfig &__getDefault__();
    static const HapticSliderConfig &__getMax__();
    static const HapticSliderConfig &__getMin__();
    static const std::vector<AbstractParamDescriptionConstPtr> &__getParamDescriptions__();
    static const std::vector<AbstractGroupDescriptionConstPtr> &__getGroupDescriptions__();

  private:
    static const HapticSliderConfigStatics *__get_statics__();
  };

  template <> // Max and min are ignored for strings.
  inline void HapticSliderConfig::ParamDescription<std::string>::clamp(HapticSliderConfig &config, const HapticSliderConfig &max, const HapticSliderConfig &min) const
  {
    (void) config;
    (void) min;
    (void) max;
    return;
  }

  class HapticSliderConfigStatics
  {
    friend class HapticSliderConfig;

    HapticSliderConfigStatics()
    {
HapticSliderConfig::GroupDescription<HapticSliderConfig::DEFAULT, HapticSliderConfig> Default("Default", "", 0, 0, true, &HapticSliderConfig::groups);
//#line 292 "/opt/ros/noetic/lib/python3/dist-packages/dynamic_reconfigure/parameter_generator_catkin.py"
      __min__.thumb_ffb = 0.0;
//#line 292 "/opt/ros/noetic/lib/python3/dist-packages/dynamic_reconfigure/parameter_generator_catkin.py"
      __max__.thumb_ffb = 100.0;
//#line 292 "/opt/ros/noetic/lib/python3/dist-packages/dynamic_reconfigure/parameter_generator_catkin.py"
      __default__.thumb_ffb = 0.0;
//#line 292 "/opt/ros/noetic/lib/python3/dist-packages/dynamic_reconfigure/parameter_generator_catkin.py"
      Default.abstract_parameters.push_back(HapticSliderConfig::AbstractParamDescriptionConstPtr(new HapticSliderConfig::ParamDescription<double>("thumb_ffb", "double", 0, "Force Feedback for the thumb finger", "", &HapticSliderConfig::thumb_ffb)));
//#line 292 "/opt/ros/noetic/lib/python3/dist-packages/dynamic_reconfigure/parameter_generator_catkin.py"
      __param_descriptions__.push_back(HapticSliderConfig::AbstractParamDescriptionConstPtr(new HapticSliderConfig::ParamDescription<double>("thumb_ffb", "double", 0, "Force Feedback for the thumb finger", "", &HapticSliderConfig::thumb_ffb)));
//#line 292 "/opt/ros/noetic/lib/python3/dist-packages/dynamic_reconfigure/parameter_generator_catkin.py"
      __min__.index_ffb = 0.0;
//#line 292 "/opt/ros/noetic/lib/python3/dist-packages/dynamic_reconfigure/parameter_generator_catkin.py"
      __max__.index_ffb = 100.0;
//#line 292 "/opt/ros/noetic/lib/python3/dist-packages/dynamic_reconfigure/parameter_generator_catkin.py"
      __default__.index_ffb = 0.0;
//#line 292 "/opt/ros/noetic/lib/python3/dist-packages/dynamic_reconfigure/parameter_generator_catkin.py"
      Default.abstract_parameters.push_back(HapticSliderConfig::AbstractParamDescriptionConstPtr(new HapticSliderConfig::ParamDescription<double>("index_ffb", "double", 0, "Force Feedback for the index finger", "", &HapticSliderConfig::index_ffb)));
//#line 292 "/opt/ros/noetic/lib/python3/dist-packages/dynamic_reconfigure/parameter_generator_catkin.py"
      __param_descriptions__.push_back(HapticSliderConfig::AbstractParamDescriptionConstPtr(new HapticSliderConfig::ParamDescription<double>("index_ffb", "double", 0, "Force Feedback for the index finger", "", &HapticSliderConfig::index_ffb)));
//#line 292 "/opt/ros/noetic/lib/python3/dist-packages/dynamic_reconfigure/parameter_generator_catkin.py"
      __min__.middle_ffb = 0.0;
//#line 292 "/opt/ros/noetic/lib/python3/dist-packages/dynamic_reconfigure/parameter_generator_catkin.py"
      __max__.middle_ffb = 100.0;
//#line 292 "/opt/ros/noetic/lib/python3/dist-packages/dynamic_reconfigure/parameter_generator_catkin.py"
      __default__.middle_ffb = 0.0;
//#line 292 "/opt/ros/noetic/lib/python3/dist-packages/dynamic_reconfigure/parameter_generator_catkin.py"
      Default.abstract_parameters.push_back(HapticSliderConfig::AbstractParamDescriptionConstPtr(new HapticSliderConfig::ParamDescription<double>("middle_ffb", "double", 0, "Force Feedback for the middle finger", "", &HapticSliderConfig::middle_ffb)));
//#line 292 "/opt/ros/noetic/lib/python3/dist-packages/dynamic_reconfigure/parameter_generator_catkin.py"
      __param_descriptions__.push_back(HapticSliderConfig::AbstractParamDescriptionConstPtr(new HapticSliderConfig::ParamDescription<double>("middle_ffb", "double", 0, "Force Feedback for the middle finger", "", &HapticSliderConfig::middle_ffb)));
//#line 292 "/opt/ros/noetic/lib/python3/dist-packages/dynamic_reconfigure/parameter_generator_catkin.py"
      __min__.ring_ffb = 0.0;
//#line 292 "/opt/ros/noetic/lib/python3/dist-packages/dynamic_reconfigure/parameter_generator_catkin.py"
      __max__.ring_ffb = 100.0;
//#line 292 "/opt/ros/noetic/lib/python3/dist-packages/dynamic_reconfigure/parameter_generator_catkin.py"
      __default__.ring_ffb = 0.0;
//#line 292 "/opt/ros/noetic/lib/python3/dist-packages/dynamic_reconfigure/parameter_generator_catkin.py"
      Default.abstract_parameters.push_back(HapticSliderConfig::AbstractParamDescriptionConstPtr(new HapticSliderConfig::ParamDescription<double>("ring_ffb", "double", 0, "Force Feedback for the ring finger", "", &HapticSliderConfig::ring_ffb)));
//#line 292 "/opt/ros/noetic/lib/python3/dist-packages/dynamic_reconfigure/parameter_generator_catkin.py"
      __param_descriptions__.push_back(HapticSliderConfig::AbstractParamDescriptionConstPtr(new HapticSliderConfig::ParamDescription<double>("ring_ffb", "double", 0, "Force Feedback for the ring finger", "", &HapticSliderConfig::ring_ffb)));
//#line 292 "/opt/ros/noetic/lib/python3/dist-packages/dynamic_reconfigure/parameter_generator_catkin.py"
      __min__.pinky_ffb = 0.0;
//#line 292 "/opt/ros/noetic/lib/python3/dist-packages/dynamic_reconfigure/parameter_generator_catkin.py"
      __max__.pinky_ffb = 100.0;
//#line 292 "/opt/ros/noetic/lib/python3/dist-packages/dynamic_reconfigure/parameter_generator_catkin.py"
      __default__.pinky_ffb = 0.0;
//#line 292 "/opt/ros/noetic/lib/python3/dist-packages/dynamic_reconfigure/parameter_generator_catkin.py"
      Default.abstract_parameters.push_back(HapticSliderConfig::AbstractParamDescriptionConstPtr(new HapticSliderConfig::ParamDescription<double>("pinky_ffb", "double", 0, "Force Feedback for the pinky finger", "", &HapticSliderConfig::pinky_ffb)));
//#line 292 "/opt/ros/noetic/lib/python3/dist-packages/dynamic_reconfigure/parameter_generator_catkin.py"
      __param_descriptions__.push_back(HapticSliderConfig::AbstractParamDescriptionConstPtr(new HapticSliderConfig::ParamDescription<double>("pinky_ffb", "double", 0, "Force Feedback for the pinky finger", "", &HapticSliderConfig::pinky_ffb)));
//#line 292 "/opt/ros/noetic/lib/python3/dist-packages/dynamic_reconfigure/parameter_generator_catkin.py"
      __min__.thumb_buzz = 0.0;
//#line 292 "/opt/ros/noetic/lib/python3/dist-packages/dynamic_reconfigure/parameter_generator_catkin.py"
      __max__.thumb_buzz = 100.0;
//#line 292 "/opt/ros/noetic/lib/python3/dist-packages/dynamic_reconfigure/parameter_generator_catkin.py"
      __default__.thumb_buzz = 0.0;
//#line 292 "/opt/ros/noetic/lib/python3/dist-packages/dynamic_reconfigure/parameter_generator_catkin.py"
      Default.abstract_parameters.push_back(HapticSliderConfig::AbstractParamDescriptionConstPtr(new HapticSliderConfig::ParamDescription<double>("thumb_buzz", "double", 0, "Vibration Feedback for the thumb finger", "", &HapticSliderConfig::thumb_buzz)));
//#line 292 "/opt/ros/noetic/lib/python3/dist-packages/dynamic_reconfigure/parameter_generator_catkin.py"
      __param_descriptions__.push_back(HapticSliderConfig::AbstractParamDescriptionConstPtr(new HapticSliderConfig::ParamDescription<double>("thumb_buzz", "double", 0, "Vibration Feedback for the thumb finger", "", &HapticSliderConfig::thumb_buzz)));
//#line 292 "/opt/ros/noetic/lib/python3/dist-packages/dynamic_reconfigure/parameter_generator_catkin.py"
      __min__.index_buzz = 0.0;
//#line 292 "/opt/ros/noetic/lib/python3/dist-packages/dynamic_reconfigure/parameter_generator_catkin.py"
      __max__.index_buzz = 100.0;
//#line 292 "/opt/ros/noetic/lib/python3/dist-packages/dynamic_reconfigure/parameter_generator_catkin.py"
      __default__.index_buzz = 0.0;
//#line 292 "/opt/ros/noetic/lib/python3/dist-packages/dynamic_reconfigure/parameter_generator_catkin.py"
      Default.abstract_parameters.push_back(HapticSliderConfig::AbstractParamDescriptionConstPtr(new HapticSliderConfig::ParamDescription<double>("index_buzz", "double", 0, "Vibration Feedback for the index finger", "", &HapticSliderConfig::index_buzz)));
//#line 292 "/opt/ros/noetic/lib/python3/dist-packages/dynamic_reconfigure/parameter_generator_catkin.py"
      __param_descriptions__.push_back(HapticSliderConfig::AbstractParamDescriptionConstPtr(new HapticSliderConfig::ParamDescription<double>("index_buzz", "double", 0, "Vibration Feedback for the index finger", "", &HapticSliderConfig::index_buzz)));
//#line 292 "/opt/ros/noetic/lib/python3/dist-packages/dynamic_reconfigure/parameter_generator_catkin.py"
      __min__.middle_buzz = 0.0;
//#line 292 "/opt/ros/noetic/lib/python3/dist-packages/dynamic_reconfigure/parameter_generator_catkin.py"
      __max__.middle_buzz = 100.0;
//#line 292 "/opt/ros/noetic/lib/python3/dist-packages/dynamic_reconfigure/parameter_generator_catkin.py"
      __default__.middle_buzz = 0.0;
//#line 292 "/opt/ros/noetic/lib/python3/dist-packages/dynamic_reconfigure/parameter_generator_catkin.py"
      Default.abstract_parameters.push_back(HapticSliderConfig::AbstractParamDescriptionConstPtr(new HapticSliderConfig::ParamDescription<double>("middle_buzz", "double", 0, "Vibration Feedback for the middle finger", "", &HapticSliderConfig::middle_buzz)));
//#line 292 "/opt/ros/noetic/lib/python3/dist-packages/dynamic_reconfigure/parameter_generator_catkin.py"
      __param_descriptions__.push_back(HapticSliderConfig::AbstractParamDescriptionConstPtr(new HapticSliderConfig::ParamDescription<double>("middle_buzz", "double", 0, "Vibration Feedback for the middle finger", "", &HapticSliderConfig::middle_buzz)));
//#line 292 "/opt/ros/noetic/lib/python3/dist-packages/dynamic_reconfigure/parameter_generator_catkin.py"
      __min__.ring_buzz = 0.0;
//#line 292 "/opt/ros/noetic/lib/python3/dist-packages/dynamic_reconfigure/parameter_generator_catkin.py"
      __max__.ring_buzz = 100.0;
//#line 292 "/opt/ros/noetic/lib/python3/dist-packages/dynamic_reconfigure/parameter_generator_catkin.py"
      __default__.ring_buzz = 0.0;
//#line 292 "/opt/ros/noetic/lib/python3/dist-packages/dynamic_reconfigure/parameter_generator_catkin.py"
      Default.abstract_parameters.push_back(HapticSliderConfig::AbstractParamDescriptionConstPtr(new HapticSliderConfig::ParamDescription<double>("ring_buzz", "double", 0, "Vibration Feedback for the ring finger", "", &HapticSliderConfig::ring_buzz)));
//#line 292 "/opt/ros/noetic/lib/python3/dist-packages/dynamic_reconfigure/parameter_generator_catkin.py"
      __param_descriptions__.push_back(HapticSliderConfig::AbstractParamDescriptionConstPtr(new HapticSliderConfig::ParamDescription<double>("ring_buzz", "double", 0, "Vibration Feedback for the ring finger", "", &HapticSliderConfig::ring_buzz)));
//#line 292 "/opt/ros/noetic/lib/python3/dist-packages/dynamic_reconfigure/parameter_generator_catkin.py"
      __min__.pinky_buzz = 0.0;
//#line 292 "/opt/ros/noetic/lib/python3/dist-packages/dynamic_reconfigure/parameter_generator_catkin.py"
      __max__.pinky_buzz = 100.0;
//#line 292 "/opt/ros/noetic/lib/python3/dist-packages/dynamic_reconfigure/parameter_generator_catkin.py"
      __default__.pinky_buzz = 0.0;
//#line 292 "/opt/ros/noetic/lib/python3/dist-packages/dynamic_reconfigure/parameter_generator_catkin.py"
      Default.abstract_parameters.push_back(HapticSliderConfig::AbstractParamDescriptionConstPtr(new HapticSliderConfig::ParamDescription<double>("pinky_buzz", "double", 0, "Vibration Feedback for the pinky finger", "", &HapticSliderConfig::pinky_buzz)));
//#line 292 "/opt/ros/noetic/lib/python3/dist-packages/dynamic_reconfigure/parameter_generator_catkin.py"
      __param_descriptions__.push_back(HapticSliderConfig::AbstractParamDescriptionConstPtr(new HapticSliderConfig::ParamDescription<double>("pinky_buzz", "double", 0, "Vibration Feedback for the pinky finger", "", &HapticSliderConfig::pinky_buzz)));
//#line 292 "/opt/ros/noetic/lib/python3/dist-packages/dynamic_reconfigure/parameter_generator_catkin.py"
      __min__.thumper_buzz = 0.0;
//#line 292 "/opt/ros/noetic/lib/python3/dist-packages/dynamic_reconfigure/parameter_generator_catkin.py"
      __max__.thumper_buzz = 100.0;
//#line 292 "/opt/ros/noetic/lib/python3/dist-packages/dynamic_reconfigure/parameter_generator_catkin.py"
      __default__.thumper_buzz = 0.0;
//#line 292 "/opt/ros/noetic/lib/python3/dist-packages/dynamic_reconfigure/parameter_generator_catkin.py"
      Default.abstract_parameters.push_back(HapticSliderConfig::AbstractParamDescriptionConstPtr(new HapticSliderConfig::ParamDescription<double>("thumper_buzz", "double", 0, "Vibration Feedback for the Thumper", "", &HapticSliderConfig::thumper_buzz)));
//#line 292 "/opt/ros/noetic/lib/python3/dist-packages/dynamic_reconfigure/parameter_generator_catkin.py"
      __param_descriptions__.push_back(HapticSliderConfig::AbstractParamDescriptionConstPtr(new HapticSliderConfig::ParamDescription<double>("thumper_buzz", "double", 0, "Vibration Feedback for the Thumper", "", &HapticSliderConfig::thumper_buzz)));
//#line 292 "/opt/ros/noetic/lib/python3/dist-packages/dynamic_reconfigure/parameter_generator_catkin.py"
      __min__.palm_index_buzz = 0.0;
//#line 292 "/opt/ros/noetic/lib/python3/dist-packages/dynamic_reconfigure/parameter_generator_catkin.py"
      __max__.palm_index_buzz = 100.0;
//#line 292 "/opt/ros/noetic/lib/python3/dist-packages/dynamic_reconfigure/parameter_generator_catkin.py"
      __default__.palm_index_buzz = 0.0;
//#line 292 "/opt/ros/noetic/lib/python3/dist-packages/dynamic_reconfigure/parameter_generator_catkin.py"
      Default.abstract_parameters.push_back(HapticSliderConfig::AbstractParamDescriptionConstPtr(new HapticSliderConfig::ParamDescription<double>("palm_index_buzz", "double", 0, "Vibration Feedback for the palm_index side", "", &HapticSliderConfig::palm_index_buzz)));
//#line 292 "/opt/ros/noetic/lib/python3/dist-packages/dynamic_reconfigure/parameter_generator_catkin.py"
      __param_descriptions__.push_back(HapticSliderConfig::AbstractParamDescriptionConstPtr(new HapticSliderConfig::ParamDescription<double>("palm_index_buzz", "double", 0, "Vibration Feedback for the palm_index side", "", &HapticSliderConfig::palm_index_buzz)));
//#line 292 "/opt/ros/noetic/lib/python3/dist-packages/dynamic_reconfigure/parameter_generator_catkin.py"
      __min__.palm_pinky_buzz = 0.0;
//#line 292 "/opt/ros/noetic/lib/python3/dist-packages/dynamic_reconfigure/parameter_generator_catkin.py"
      __max__.palm_pinky_buzz = 100.0;
//#line 292 "/opt/ros/noetic/lib/python3/dist-packages/dynamic_reconfigure/parameter_generator_catkin.py"
      __default__.palm_pinky_buzz = 0.0;
//#line 292 "/opt/ros/noetic/lib/python3/dist-packages/dynamic_reconfigure/parameter_generator_catkin.py"
      Default.abstract_parameters.push_back(HapticSliderConfig::AbstractParamDescriptionConstPtr(new HapticSliderConfig::ParamDescription<double>("palm_pinky_buzz", "double", 0, "Vibration Feedback for the palm_pinky side", "", &HapticSliderConfig::palm_pinky_buzz)));
//#line 292 "/opt/ros/noetic/lib/python3/dist-packages/dynamic_reconfigure/parameter_generator_catkin.py"
      __param_descriptions__.push_back(HapticSliderConfig::AbstractParamDescriptionConstPtr(new HapticSliderConfig::ParamDescription<double>("palm_pinky_buzz", "double", 0, "Vibration Feedback for the palm_pinky side", "", &HapticSliderConfig::palm_pinky_buzz)));
//#line 292 "/opt/ros/noetic/lib/python3/dist-packages/dynamic_reconfigure/parameter_generator_catkin.py"
      __min__.palm_strap = 0.0;
//#line 292 "/opt/ros/noetic/lib/python3/dist-packages/dynamic_reconfigure/parameter_generator_catkin.py"
      __max__.palm_strap = 100.0;
//#line 292 "/opt/ros/noetic/lib/python3/dist-packages/dynamic_reconfigure/parameter_generator_catkin.py"
      __default__.palm_strap = 0.0;
//#line 292 "/opt/ros/noetic/lib/python3/dist-packages/dynamic_reconfigure/parameter_generator_catkin.py"
      Default.abstract_parameters.push_back(HapticSliderConfig::AbstractParamDescriptionConstPtr(new HapticSliderConfig::ParamDescription<double>("palm_strap", "double", 0, "Active Strap Squeeze", "", &HapticSliderConfig::palm_strap)));
//#line 292 "/opt/ros/noetic/lib/python3/dist-packages/dynamic_reconfigure/parameter_generator_catkin.py"
      __param_descriptions__.push_back(HapticSliderConfig::AbstractParamDescriptionConstPtr(new HapticSliderConfig::ParamDescription<double>("palm_strap", "double", 0, "Active Strap Squeeze", "", &HapticSliderConfig::palm_strap)));
//#line 292 "/opt/ros/noetic/lib/python3/dist-packages/dynamic_reconfigure/parameter_generator_catkin.py"
      __min__.reset = 0;
//#line 292 "/opt/ros/noetic/lib/python3/dist-packages/dynamic_reconfigure/parameter_generator_catkin.py"
      __max__.reset = 1;
//#line 292 "/opt/ros/noetic/lib/python3/dist-packages/dynamic_reconfigure/parameter_generator_catkin.py"
      __default__.reset = 0;
//#line 292 "/opt/ros/noetic/lib/python3/dist-packages/dynamic_reconfigure/parameter_generator_catkin.py"
      Default.abstract_parameters.push_back(HapticSliderConfig::AbstractParamDescriptionConstPtr(new HapticSliderConfig::ParamDescription<bool>("reset", "bool", 0, "A Boolean parameter to reset", "", &HapticSliderConfig::reset)));
//#line 292 "/opt/ros/noetic/lib/python3/dist-packages/dynamic_reconfigure/parameter_generator_catkin.py"
      __param_descriptions__.push_back(HapticSliderConfig::AbstractParamDescriptionConstPtr(new HapticSliderConfig::ParamDescription<bool>("reset", "bool", 0, "A Boolean parameter to reset", "", &HapticSliderConfig::reset)));
//#line 247 "/opt/ros/noetic/lib/python3/dist-packages/dynamic_reconfigure/parameter_generator_catkin.py"
      Default.convertParams();
//#line 247 "/opt/ros/noetic/lib/python3/dist-packages/dynamic_reconfigure/parameter_generator_catkin.py"
      __group_descriptions__.push_back(HapticSliderConfig::AbstractGroupDescriptionConstPtr(new HapticSliderConfig::GroupDescription<HapticSliderConfig::DEFAULT, HapticSliderConfig>(Default)));
//#line 369 "/opt/ros/noetic/share/dynamic_reconfigure/cmake/../templates/ConfigType.h.template"

      for (std::vector<HapticSliderConfig::AbstractGroupDescriptionConstPtr>::const_iterator i = __group_descriptions__.begin(); i != __group_descriptions__.end(); ++i)
      {
        __description_message__.groups.push_back(**i);
      }
      __max__.__toMessage__(__description_message__.max, __param_descriptions__, __group_descriptions__);
      __min__.__toMessage__(__description_message__.min, __param_descriptions__, __group_descriptions__);
      __default__.__toMessage__(__description_message__.dflt, __param_descriptions__, __group_descriptions__);
    }
    std::vector<HapticSliderConfig::AbstractParamDescriptionConstPtr> __param_descriptions__;
    std::vector<HapticSliderConfig::AbstractGroupDescriptionConstPtr> __group_descriptions__;
    HapticSliderConfig __max__;
    HapticSliderConfig __min__;
    HapticSliderConfig __default__;
    dynamic_reconfigure::ConfigDescription __description_message__;

    static const HapticSliderConfigStatics *get_instance()
    {
      // Split this off in a separate function because I know that
      // instance will get initialized the first time get_instance is
      // called, and I am guaranteeing that get_instance gets called at
      // most once.
      static HapticSliderConfigStatics instance;
      return &instance;
    }
  };

  inline const dynamic_reconfigure::ConfigDescription &HapticSliderConfig::__getDescriptionMessage__()
  {
    return __get_statics__()->__description_message__;
  }

  inline const HapticSliderConfig &HapticSliderConfig::__getDefault__()
  {
    return __get_statics__()->__default__;
  }

  inline const HapticSliderConfig &HapticSliderConfig::__getMax__()
  {
    return __get_statics__()->__max__;
  }

  inline const HapticSliderConfig &HapticSliderConfig::__getMin__()
  {
    return __get_statics__()->__min__;
  }

  inline const std::vector<HapticSliderConfig::AbstractParamDescriptionConstPtr> &HapticSliderConfig::__getParamDescriptions__()
  {
    return __get_statics__()->__param_descriptions__;
  }

  inline const std::vector<HapticSliderConfig::AbstractGroupDescriptionConstPtr> &HapticSliderConfig::__getGroupDescriptions__()
  {
    return __get_statics__()->__group_descriptions__;
  }

  inline const HapticSliderConfigStatics *HapticSliderConfig::__get_statics__()
  {
    const static HapticSliderConfigStatics *statics;

    if (statics) // Common case
      return statics;

    boost::mutex::scoped_lock lock(dynamic_reconfigure::__init_mutex__);

    if (statics) // In case we lost a race.
      return statics;

    statics = HapticSliderConfigStatics::get_instance();

    return statics;
  }


}

#undef DYNAMIC_RECONFIGURE_FINAL

#endif // __HAPTICSLIDERRECONFIGURATOR_H__
