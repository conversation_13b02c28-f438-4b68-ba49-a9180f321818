<?xml version="1.0" ?>
<!-- =================================================================================== -->
<!-- |    This document was autogenerated by xacro from urdf/dk1_left.xacro            | -->
<!-- |    EDITING THIS FILE BY HAND IS NOT RECOMMENDED                                 | -->
<!-- =================================================================================== -->
<!-- Revolute-Revolute Manipulator -->
<robot name="dk1">
  <!-- Cube dimensions (width x width x width) of joint base -->
  <!-- rad/s -->
  <!-- Used for fixing robot to Gazebo 'world' -->
  <link name="world"/>
  <!-- glove hub -->
  <link name="l_glove_hub">
    <visual>
      <origin rpy="0 0 0" xyz="0.056692 -0.01955 -0.06803"/>
      <geometry>
        <mesh filename="package://senseglove_description/urdf/STL/MK29_DK1_HUB.STL" scale="-0.001 0.001 0.001"/>
      </geometry>
    </visual>
  </link>
  <!-- thumb brake -->
  <link name="l_thumb_brake">
    <visual>
      <origin rpy="0 0 0" xyz="-0.0111395 -0.01815 -0.11359999999999999"/>
      <geometry>
        <mesh filename="package://senseglove_description/urdf/STL/MK29_DK1_Brake_assembly_v2.STL" scale="0.001 0.001 0.001"/>
      </geometry>
    </visual>
  </link>
  <!-- thumb abduction -->
  <link name="l_thumb_abduction">
    <visual>
      <origin rpy="0 0 0" xyz="-0.0111395 -0.025300000000000003 -0.1166"/>
      <geometry>
        <mesh filename="package://senseglove_description/urdf/STL/MK29_DK1_Abduction_joint_v2.STL" scale="0.001 0.001 0.001"/>
      </geometry>
    </visual>
  </link>
  <!-- thumb flexion one -->
  <link name="l_thumb_flexion_one">
    <visual>
      <origin rpy="0 0 0" xyz="-0.0111395 -0.0263 -0.0931"/>
      <geometry>
        <mesh filename="package://senseglove_description/urdf/STL/MK29_DK1_Flexion_joint_1_v2.STL" scale="0.001 0.001 0.001"/>
      </geometry>
    </visual>
  </link>
  <!-- thumb flexion two -->
  <link name="l_thumb_flexion_two">
    <visual>
      <origin rpy="0 0 0" xyz="-0.0111395 -0.0263 -0.092955"/>
      <geometry>
        <mesh filename="package://senseglove_description/urdf/STL/MK29_DK1_Flexion_joint_2_v2.STL" scale="0.001 0.001 0.001"/>
      </geometry>
    </visual>
  </link>
  <!-- thumb flexion three -->
  <link name="l_thumb_flexion_three">
    <visual>
      <origin rpy="0 0 0" xyz="-0.0111395 -0.02597 -0.00533"/>
      <geometry>
        <mesh filename="package://senseglove_description/urdf/STL/MK29_DK1_Flexion_joint_3_v2.STL" scale="0.001 0.001 0.001"/>
      </geometry>
    </visual>
  </link>
  <!-- thumb thimble -->
  <link name="l_thumb_thimble">
    <visual>
      <origin rpy="0 0 0" xyz="-0.0111395 -0.014951 -0.0692"/>
      <geometry>
        <mesh filename="package://senseglove_description/urdf/STL/MK29_DK1_Thimble_v2.STL" scale="0.001 0.001 0.001"/>
      </geometry>
    </visual>
  </link>
  <!-- index brake -->
  <link name="l_index_brake">
    <visual>
      <origin rpy="0 0 0" xyz="-0.0111395 -0.01815 -0.11359999999999999"/>
      <geometry>
        <mesh filename="package://senseglove_description/urdf/STL/MK29_DK1_Brake_assembly_v2.STL" scale="0.001 0.001 0.001"/>
      </geometry>
    </visual>
  </link>
  <!-- index abduction -->
  <link name="l_index_abduction">
    <visual>
      <origin rpy="0 0 0" xyz="-0.0111395 -0.025300000000000003 -0.1166"/>
      <geometry>
        <mesh filename="package://senseglove_description/urdf/STL/MK29_DK1_Abduction_joint_v2.STL" scale="0.001 0.001 0.001"/>
      </geometry>
    </visual>
  </link>
  <!-- index flexion one -->
  <link name="l_index_flexion_one">
    <visual>
      <origin rpy="0 0 0" xyz="-0.0111395 -0.0263 -0.0931"/>
      <geometry>
        <mesh filename="package://senseglove_description/urdf/STL/MK29_DK1_Flexion_joint_1_v2.STL" scale="0.001 0.001 0.001"/>
      </geometry>
    </visual>
  </link>
  <!-- index flexion two -->
  <link name="l_index_flexion_two">
    <visual>
      <origin rpy="0 0 0" xyz="-0.0111395 -0.0263 -0.092955"/>
      <geometry>
        <mesh filename="package://senseglove_description/urdf/STL/MK29_DK1_Flexion_joint_2_v2.STL" scale="0.001 0.001 0.001"/>
      </geometry>
    </visual>
  </link>
  <!-- index flexion three -->
  <link name="l_index_flexion_three">
    <visual>
      <origin rpy="0 0 0" xyz="-0.0111395 -0.02597 -0.00533"/>
      <geometry>
        <mesh filename="package://senseglove_description/urdf/STL/MK29_DK1_Flexion_joint_3_v2.STL" scale="0.001 0.001 0.001"/>
      </geometry>
    </visual>
  </link>
  <!-- index thimble -->
  <link name="l_index_thimble">
    <visual>
      <origin rpy="0 0 0" xyz="-0.0111395 -0.014951 -0.0692"/>
      <geometry>
        <mesh filename="package://senseglove_description/urdf/STL/MK29_DK1_Thimble_v2.STL" scale="0.001 0.001 0.001"/>
      </geometry>
    </visual>
  </link>
  <!-- middle brake -->
  <link name="l_middle_brake">
    <visual>
      <origin rpy="0 0 0" xyz="-0.0111395 -0.01815 -0.11359999999999999"/>
      <geometry>
        <mesh filename="package://senseglove_description/urdf/STL/MK29_DK1_Brake_assembly_v2.STL" scale="0.001 0.001 0.001"/>
      </geometry>
    </visual>
  </link>
  <!-- middle abduction -->
  <link name="l_middle_abduction">
    <visual>
      <origin rpy="0 0 0" xyz="-0.0111395 -0.025300000000000003 -0.1166"/>
      <geometry>
        <mesh filename="package://senseglove_description/urdf/STL/MK29_DK1_Abduction_joint_v2.STL" scale="0.001 0.001 0.001"/>
      </geometry>
    </visual>
  </link>
  <!-- middle flexion one -->
  <link name="l_middle_flexion_one">
    <visual>
      <origin rpy="0 0 0" xyz="-0.0111395 -0.0263 -0.0931"/>
      <geometry>
        <mesh filename="package://senseglove_description/urdf/STL/MK29_DK1_Flexion_joint_1_v2.STL" scale="0.001 0.001 0.001"/>
      </geometry>
    </visual>
  </link>
  <!-- middle flexion two -->
  <link name="l_middle_flexion_two">
    <visual>
      <origin rpy="0 0 0" xyz="-0.0111395 -0.0263 -0.092955"/>
      <geometry>
        <mesh filename="package://senseglove_description/urdf/STL/MK29_DK1_Flexion_joint_2_v2.STL" scale="0.001 0.001 0.001"/>
      </geometry>
    </visual>
  </link>
  <!-- middle flexion three -->
  <link name="l_middle_flexion_three">
    <visual>
      <origin rpy="0 0 0" xyz="-0.0111395 -0.02597 -0.00533"/>
      <geometry>
        <mesh filename="package://senseglove_description/urdf/STL/MK29_DK1_Flexion_joint_3_v2.STL" scale="0.001 0.001 0.001"/>
      </geometry>
    </visual>
  </link>
  <!-- middle thimble -->
  <link name="l_middle_thimble">
    <visual>
      <origin rpy="0 0 0" xyz="-0.0111395 -0.014951 -0.0692"/>
      <geometry>
        <mesh filename="package://senseglove_description/urdf/STL/MK29_DK1_Thimble_v2.STL" scale="0.001 0.001 0.001"/>
      </geometry>
    </visual>
  </link>
  <!-- ring brake -->
  <link name="l_ring_brake">
    <visual>
      <origin rpy="0 0 0" xyz="-0.0111395 -0.01815 -0.11359999999999999"/>
      <geometry>
        <mesh filename="package://senseglove_description/urdf/STL/MK29_DK1_Brake_assembly_v2.STL" scale="0.001 0.001 0.001"/>
      </geometry>
    </visual>
  </link>
  <!-- ring abduction -->
  <link name="l_ring_abduction">
    <visual>
      <origin rpy="0 0 0" xyz="-0.0111395 -0.025300000000000003 -0.1166"/>
      <geometry>
        <mesh filename="package://senseglove_description/urdf/STL/MK29_DK1_Abduction_joint_v2.STL" scale="0.001 0.001 0.001"/>
      </geometry>
    </visual>
  </link>
  <!-- ring flexion one -->
  <link name="l_ring_flexion_one">
    <visual>
      <origin rpy="0 0 0" xyz="-0.0111395 -0.0263 -0.0931"/>
      <geometry>
        <mesh filename="package://senseglove_description/urdf/STL/MK29_DK1_Flexion_joint_1_v2.STL" scale="0.001 0.001 0.001"/>
      </geometry>
    </visual>
  </link>
  <!-- ring flexion two -->
  <link name="l_ring_flexion_two">
    <visual>
      <origin rpy="0 0 0" xyz="-0.0111395 -0.0263 -0.092955"/>
      <geometry>
        <mesh filename="package://senseglove_description/urdf/STL/MK29_DK1_Flexion_joint_2_v2.STL" scale="0.001 0.001 0.001"/>
      </geometry>
    </visual>
  </link>
  <!-- ring flexion three -->
  <link name="l_ring_flexion_three">
    <visual>
      <origin rpy="0 0 0" xyz="-0.0111395 -0.02597 -0.00533"/>
      <geometry>
        <mesh filename="package://senseglove_description/urdf/STL/MK29_DK1_Flexion_joint_3_v2.STL" scale="0.001 0.001 0.001"/>
      </geometry>
    </visual>
  </link>
  <!-- ring thimble -->
  <link name="l_ring_thimble">
    <visual>
      <origin rpy="0 0 0" xyz="-0.0111395 -0.014951 -0.0692"/>
      <geometry>
        <mesh filename="package://senseglove_description/urdf/STL/MK29_DK1_Thimble_v2.STL" scale="0.001 0.001 0.001"/>
      </geometry>
    </visual>
  </link>
  <!-- pinky brake -->
  <link name="l_pinky_brake">
    <visual>
      <origin rpy="0 0 0" xyz="-0.0115395 -0.01815 -0.1166"/>
      <geometry>
        <mesh filename="package://senseglove_description/urdf/STL/MK29_DK1_Brake_assembly_v2.STL" scale="0.001 0.001 0.001"/>
      </geometry>
    </visual>
  </link>
  <!-- pinky abduction -->
  <link name="l_pinky_abduction">
    <visual>
      <origin rpy="0 0 0" xyz="-0.0111395 -0.025300000000000003 -0.1166"/>
      <geometry>
        <mesh filename="package://senseglove_description/urdf/STL/MK29_DK1_Abduction_joint_v2.STL" scale="0.001 0.001 0.001"/>
      </geometry>
    </visual>
  </link>
  <!-- pinky flexion one -->
  <link name="l_pinky_flexion_one">
    <visual>
      <origin rpy="0 0 0" xyz="-0.0111395 -0.0263 -0.0931"/>
      <geometry>
        <mesh filename="package://senseglove_description/urdf/STL/MK29_DK1_Flexion_joint_1_v2.STL" scale="0.001 0.001 0.001"/>
      </geometry>
    </visual>
  </link>
  <!-- pinky flexion two -->
  <link name="l_pinky_flexion_two">
    <visual>
      <origin rpy="0 0 0" xyz="-0.0111395 -0.0263 -0.092955"/>
      <geometry>
        <mesh filename="package://senseglove_description/urdf/STL/MK29_DK1_Flexion_joint_2_v2.STL" scale="0.001 0.001 0.001"/>
      </geometry>
    </visual>
  </link>
  <!-- pinky flexion three -->
  <link name="l_pinky_flexion_three">
    <visual>
      <origin rpy="0 0 0" xyz="-0.0111395 -0.02597 -0.00533"/>
      <geometry>
        <mesh filename="package://senseglove_description/urdf/STL/MK29_DK1_Flexion_joint_3_v2.STL" scale="0.001 0.001 0.001"/>
      </geometry>
    </visual>
  </link>
  <!-- pinky thimble -->
  <link name="l_pinky_thimble">
    <visual>
      <origin rpy="0 0 0" xyz="-0.0111395 -0.014951 -0.0692"/>
      <geometry>
        <mesh filename="package://senseglove_description/urdf/STL/MK29_DK1_Thimble_v2.STL" scale="0.001 0.001 0.001"/>
      </geometry>
    </visual>
  </link>
  <!-- Joints -->
  <joint name="l_joint_base_fixed_parent_joint" type="fixed">
    <parent link="world"/>
    <child link="l_glove_hub"/>
    <origin rpy="0 0 0" xyz="-0.5 0 0.5"/>
  </joint>
  <joint name="l_hub_to_thumb_brake" type="fixed">
    <parent link="l_glove_hub"/>
    <child link="l_thumb_brake"/>
    <origin rpy="1.9460421159736774 -0.6981317007977318 -2.9670597283903604" xyz="0.052034 -0.0221293 0.04656"/>
  </joint>
  <joint name="l_thumb_brake" type="revolute">
    <parent link="l_thumb_brake"/>
    <child link="l_thumb_abduction"/>
    <origin rpy="0 0 0" xyz="0 0.0074 -0.0"/>
    <axis xyz="0 -1 0"/>
    <limit effort="100" lower="-2" upper="100" velocity="1000"/>
  </joint>
  <joint name="l_thumb_abduction" type="fixed">
    <parent link="l_thumb_abduction"/>
    <child link="l_thumb_flexion_one"/>
    <origin rpy="0 0 0" xyz="0 0 -0.02325"/>
  </joint>
  <joint name="l_thumb_mcp" type="revolute">
    <parent link="l_thumb_flexion_one"/>
    <child link="l_thumb_flexion_two"/>
    <origin rpy="0 0 0" xyz="0 0 0"/>
    <axis xyz="-1 0 0"/>
    <limit effort="100" lower="-2" upper="100" velocity="1000"/>
  </joint>
  <joint name="l_thumb_pip" type="revolute">
    <parent link="l_thumb_flexion_two"/>
    <child link="l_thumb_flexion_three"/>
    <origin rpy="0 0 0" xyz="0 0 -0.0876"/>
    <axis xyz="-1 0 0"/>
    <limit effort="100" lower="0" upper="100" velocity="1000"/>
  </joint>
  <joint name="l_thumb_dip" type="revolute">
    <parent link="l_thumb_flexion_three"/>
    <child link="l_thumb_thimble"/>
    <origin rpy="0 0 0" xyz="0 -0.012342 0.0639"/>
    <axis xyz="-1 0 0"/>
    <limit effort="100" lower="-4.2" upper="100" velocity="1000"/>
  </joint>
  <joint name="l_hub_to_index_brake" type="fixed">
    <parent link="l_glove_hub"/>
    <child link="l_index_brake"/>
    <origin rpy="0 0 0" xyz="0.03307 0.00645 -0.04436"/>
  </joint>
  <joint name="l_index_brake" type="revolute">
    <parent link="l_index_brake"/>
    <child link="l_index_abduction"/>
    <origin rpy="0 0 0" xyz="0 0.0074 -0.0"/>
    <axis xyz="0 -1 0"/>
    <limit effort="100" lower="-2" upper="100" velocity="1000"/>
  </joint>
  <joint name="l_index_abduction" type="fixed">
    <parent link="l_index_abduction"/>
    <child link="l_index_flexion_one"/>
    <origin rpy="0 0 0" xyz="0 0 -0.02325"/>
  </joint>
  <joint name="l_index_mcp" type="revolute">
    <parent link="l_index_flexion_one"/>
    <child link="l_index_flexion_two"/>
    <origin rpy="0 0 0" xyz="0 0 0"/>
    <axis xyz="-1 0 0"/>
    <limit effort="100" lower="-2" upper="100" velocity="1000"/>
  </joint>
  <joint name="l_index_pip" type="revolute">
    <parent link="l_index_flexion_two"/>
    <child link="l_index_flexion_three"/>
    <origin rpy="0 0 0" xyz="0 0 -0.0876"/>
    <axis xyz="-1 0 0"/>
    <limit effort="100" lower="0" upper="100" velocity="1000"/>
  </joint>
  <joint name="l_index_dip" type="revolute">
    <parent link="l_index_flexion_three"/>
    <child link="l_index_thimble"/>
    <origin rpy="0 0 0" xyz="0 -0.012342 0.0639"/>
    <axis xyz="-1 0 0"/>
    <limit effort="100" lower="-4.2" upper="100" velocity="1000"/>
  </joint>
  <joint name="l_hub_to_middle_brake" type="fixed">
    <parent link="l_glove_hub"/>
    <child link="l_middle_brake"/>
    <origin rpy="0 0 0" xyz="0.011 0.008 -0.06222"/>
  </joint>
  <joint name="l_middle_brake" type="revolute">
    <parent link="l_middle_brake"/>
    <child link="l_middle_abduction"/>
    <origin rpy="0 0 0" xyz="0 0.0074 -0.0"/>
    <axis xyz="0 -1 0"/>
    <limit effort="100" lower="-2" upper="100" velocity="1000"/>
  </joint>
  <joint name="l_middle_abduction" type="fixed">
    <parent link="l_middle_abduction"/>
    <child link="l_middle_flexion_one"/>
    <origin rpy="0 0 0" xyz="0 0 -0.02325"/>
    
-
    
  </joint>
  <joint name="l_middle_mcp" type="revolute">
    <parent link="l_middle_flexion_one"/>
    <child link="l_middle_flexion_two"/>
    <origin rpy="0 0 0" xyz="0 0 0"/>
    <axis xyz="-1 0 0"/>
    <limit effort="100" lower="-2" upper="100" velocity="1000"/>
  </joint>
  <joint name="l_middle_pip" type="revolute">
    <parent link="l_middle_flexion_two"/>
    <child link="l_middle_flexion_three"/>
    <origin rpy="0 0 0" xyz="0 0 -0.0876"/>
    <axis xyz="-1 0 0"/>
    <limit effort="100" lower="0" upper="100" velocity="1000"/>
  </joint>
  <joint name="l_middle_dip" type="revolute">
    <parent link="l_middle_flexion_three"/>
    <child link="l_middle_thimble"/>
    <origin rpy="0 0 0" xyz="0 -0.012342 0.0639"/>
    <axis xyz="-1 0 0"/>
    <limit effort="100" lower="-4.2" upper="100" velocity="1000"/>
  </joint>
  <joint name="l_hub_to_ring_brake" type="fixed">
    <parent link="l_glove_hub"/>
    <child link="l_ring_brake"/>
    <origin rpy="0 0 0" xyz="-0.011 0.008 -0.05422"/>
  </joint>
  <joint name="l_ring_brake" type="revolute">
    <parent link="l_ring_brake"/>
    <child link="l_ring_abduction"/>
    <origin rpy="0 0 0" xyz="0 0.0074 -0.0"/>
    <axis xyz="0 -1 0"/>
    <limit effort="100" lower="-2" upper="100" velocity="1000"/>
  </joint>
  <joint name="l_ring_abduction" type="fixed">
    <parent link="l_ring_abduction"/>
    <child link="l_ring_flexion_one"/>
    <origin rpy="0 0 0" xyz="0 0 -0.02325"/>
  </joint>
  <joint name="l_ring_mcp" type="revolute">
    <parent link="l_ring_flexion_one"/>
    <child link="l_ring_flexion_two"/>
    <origin rpy="0 0 0" xyz="0 0 0"/>
    <axis xyz="-1 0 0"/>
    <limit effort="100" lower="-2" upper="100" velocity="1000"/>
  </joint>
  <joint name="l_ring_pip" type="revolute">
    <parent link="l_ring_flexion_two"/>
    <child link="l_ring_flexion_three"/>
    <origin rpy="0 0 0" xyz="0 0 -0.0876"/>
    <axis xyz="-1 0 0"/>
    <limit effort="100" lower="0" upper="100" velocity="1000"/>
  </joint>
  <joint name="l_ring_dip" type="revolute">
    <parent link="l_ring_flexion_three"/>
    <child link="l_ring_thimble"/>
    <origin rpy="0 0 0" xyz="0 -0.012342 0.0639"/>
    <axis xyz="-1 0 0"/>
    <limit effort="100" lower="-4.2" upper="100" velocity="1000"/>
  </joint>
  <joint name="l_hub_to_pinky_brake" type="fixed">
    <parent link="l_glove_hub"/>
    <child link="l_pinky_brake"/>
    <origin rpy="0 0 0" xyz="-0.03309 0.00695 -0.04125"/>
  </joint>
  <joint name="l_pinky_brake" type="revolute">
    <parent link="l_pinky_brake"/>
    <child link="l_pinky_abduction"/>
    <origin rpy="0 0 0" xyz="0 0.0074 -0.0"/>
    <axis xyz="0 -1 0"/>
    <limit effort="100" lower="-2" upper="100" velocity="1000"/>
  </joint>
  <joint name="l_pinky_abduction" type="fixed">
    <parent link="l_pinky_abduction"/>
    <child link="l_pinky_flexion_one"/>
    <origin rpy="0 0 0" xyz="0 0 -0.02325"/>
  </joint>
  <joint name="l_pinky_mcp" type="revolute">
    <parent link="l_pinky_flexion_one"/>
    <child link="l_pinky_flexion_two"/>
    <origin rpy="0 0 0" xyz="0 0 0"/>
    <axis xyz="-1 0 0"/>
    <limit effort="100" lower="-2" upper="100" velocity="1000"/>
  </joint>
  <joint name="l_pinky_pip" type="revolute">
    <parent link="l_pinky_flexion_two"/>
    <child link="l_pinky_flexion_three"/>
    <origin rpy="0 0 0" xyz="0 0 -0.0876"/>
    <axis xyz="-1 0 0"/>
    <limit effort="100" lower="0" upper="100" velocity="1000"/>
  </joint>
  <joint name="l_pinky_dip" type="revolute">
    <parent link="l_pinky_flexion_three"/>
    <child link="l_pinky_thimble"/>
    <origin rpy="0 0 0" xyz="0 -0.012342 0.0639"/>
    <axis xyz="-1 0 0"/>
    <limit effort="100" lower="-4.2" upper="100" velocity="1000"/>
  </joint>
</robot>
