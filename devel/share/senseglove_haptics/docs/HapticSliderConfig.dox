\subsubsection parameters ROS parameters

Reads and maintains the following parameters on the ROS server

- \b "~thumb_ffb" : \b [double] Force Feedback for the thumb finger min: 0.0, default: 0.0, max: 100.0
- \b "~index_ffb" : \b [double] Force Feedback for the index finger min: 0.0, default: 0.0, max: 100.0
- \b "~middle_ffb" : \b [double] Force Feedback for the middle finger min: 0.0, default: 0.0, max: 100.0
- \b "~ring_ffb" : \b [double] Force Feedback for the ring finger min: 0.0, default: 0.0, max: 100.0
- \b "~pinky_ffb" : \b [double] Force Feedback for the pinky finger min: 0.0, default: 0.0, max: 100.0
- \b "~thumb_buzz" : \b [double] Vibration Feedback for the thumb finger min: 0.0, default: 0.0, max: 100.0
- \b "~index_buzz" : \b [double] Vibration Feedback for the index finger min: 0.0, default: 0.0, max: 100.0
- \b "~middle_buzz" : \b [double] Vibration Feedback for the middle finger min: 0.0, default: 0.0, max: 100.0
- \b "~ring_buzz" : \b [double] Vibration Feedback for the ring finger min: 0.0, default: 0.0, max: 100.0
- \b "~pinky_buzz" : \b [double] Vibration Feedback for the pinky finger min: 0.0, default: 0.0, max: 100.0
- \b "~thumper_buzz" : \b [double] Vibration Feedback for the Thumper min: 0.0, default: 0.0, max: 100.0
- \b "~palm_index_buzz" : \b [double] Vibration Feedback for the palm_index side min: 0.0, default: 0.0, max: 100.0
- \b "~palm_pinky_buzz" : \b [double] Vibration Feedback for the palm_pinky side min: 0.0, default: 0.0, max: 100.0
- \b "~palm_strap" : \b [double] Active Strap Squeeze min: 0.0, default: 0.0, max: 100.0
- \b "~reset" : \b [bool] A Boolean parameter to reset min: False, default: False, max: True

