# Autogenerated param section. Do not hand edit.
param {
group.0 {
name=Dynamically Reconfigurable Parameters
desc=See the [[dynamic_reconfigure]] package for details on dynamically reconfigurable parameters.
0.name= ~thumb_ffb
0.default= 0.0
0.type= double
0.desc=Force Feedback for the thumb finger Range: 0.0 to 100.0
1.name= ~index_ffb
1.default= 0.0
1.type= double
1.desc=Force Feedback for the index finger Range: 0.0 to 100.0
2.name= ~middle_ffb
2.default= 0.0
2.type= double
2.desc=Force Feedback for the middle finger Range: 0.0 to 100.0
3.name= ~ring_ffb
3.default= 0.0
3.type= double
3.desc=Force Feedback for the ring finger Range: 0.0 to 100.0
4.name= ~pinky_ffb
4.default= 0.0
4.type= double
4.desc=Force Feedback for the pinky finger Range: 0.0 to 100.0
5.name= ~thumb_buzz
5.default= 0.0
5.type= double
5.desc=Vibration Feedback for the thumb finger Range: 0.0 to 100.0
6.name= ~index_buzz
6.default= 0.0
6.type= double
6.desc=Vibration Feedback for the index finger Range: 0.0 to 100.0
7.name= ~middle_buzz
7.default= 0.0
7.type= double
7.desc=Vibration Feedback for the middle finger Range: 0.0 to 100.0
8.name= ~ring_buzz
8.default= 0.0
8.type= double
8.desc=Vibration Feedback for the ring finger Range: 0.0 to 100.0
9.name= ~pinky_buzz
9.default= 0.0
9.type= double
9.desc=Vibration Feedback for the pinky finger Range: 0.0 to 100.0
10.name= ~thumper_buzz
10.default= 0.0
10.type= double
10.desc=Vibration Feedback for the Thumper Range: 0.0 to 100.0
11.name= ~palm_index_buzz
11.default= 0.0
11.type= double
11.desc=Vibration Feedback for the palm_index side Range: 0.0 to 100.0
12.name= ~palm_pinky_buzz
12.default= 0.0
12.type= double
12.desc=Vibration Feedback for the palm_pinky side Range: 0.0 to 100.0
13.name= ~palm_strap
13.default= 0.0
13.type= double
13.desc=Active Strap Squeeze Range: 0.0 to 100.0
14.name= ~reset
14.default= False
14.type= bool
14.desc=A Boolean parameter to reset 
}
}
# End of autogenerated section. You may edit below.
